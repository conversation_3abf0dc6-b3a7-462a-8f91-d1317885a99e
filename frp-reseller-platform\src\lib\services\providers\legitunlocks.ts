// LegitUnlocks API Provider Implementation

import { BaseApiProvider } from './base';
import { 
  ApiServiceRequest, 
  ApiServiceResponse, 
  ApiStatusResponse, 
  DeviceInfo, 
  ServiceValidation,
  DeviceValidation,
  OrderResult
} from '@/types/services';

interface LegitUnlocksService {
  id: string;
  name: string;
  category: string;
  price: number;
  time: number;
  brands: string[];
  requirements: string[];
}

interface LegitUnlocksOrderRequest {
  service_id: string;
  imei?: string;
  model?: string;
  brand?: string;
  serial?: string;
  additional_info?: Record<string, any>;
}

interface LegitUnlocksOrderResponse {
  success: boolean;
  order_id?: string;
  status: string;
  message: string;
  estimated_time?: number;
  price?: number;
}

interface LegitUnlocksStatusResponse {
  success: boolean;
  status: string;
  message: string;
  result?: {
    unlock_code?: string;
    instructions?: string;
    files?: Array<{
      name: string;
      url: string;
      type: string;
    }>;
  };
}

export class LegitUnlocksProvider extends BaseApiProvider {
  private services: LegitUnlocksService[] = [];
  private lastServicesFetch: number = 0;
  private servicesCacheTime: number = 3600000; // 1 hour

  constructor(apiKey: string, configuration: Record<string, any> = {}) {
    super(
      'legitunlocks',
      process.env.LEGITUNLOCKS_API_URL || 'https://api.legitunlocks.com',
      apiKey,
      {
        timeout: 30000,
        retryAttempts: 3,
        rateLimitDelay: 2000,
        ...configuration
      }
    );
  }

  async submitOrder(request: ApiServiceRequest): Promise<ApiServiceResponse> {
    try {
      this.log('info', 'Submitting order to LegitUnlocks', { service_id: request.service_id });

      // Validate the request first
      const validation = await this.validateService(request.service_id, request.device_info);
      if (!validation.isValid) {
        return {
          success: false,
          status: 'failed',
          message: `Validation failed: ${validation.errors.join(', ')}`,
          error: 'VALIDATION_ERROR'
        };
      }

      // Prepare the order request
      const orderRequest: LegitUnlocksOrderRequest = {
        service_id: request.service_id,
        imei: request.device_info.imei,
        model: request.device_info.model,
        brand: request.device_info.brand,
        serial: request.device_info.serial_number,
        additional_info: {
          android_version: request.device_info.android_version,
          ios_version: request.device_info.ios_version,
          reference_id: request.reference_id,
          callback_url: request.callback_url
        }
      };

      // Submit to LegitUnlocks API
      const response = await this.withRetry(async () => {
        return await this.makeRequest('/orders', 'POST', orderRequest);
      });

      const legitResponse = response as LegitUnlocksOrderResponse;

      if (legitResponse.success) {
        this.log('info', 'Order submitted successfully', { order_id: legitResponse.order_id });
        
        return {
          success: true,
          order_id: legitResponse.order_id,
          status: this.mapOrderStatus(legitResponse.status),
          message: legitResponse.message,
          estimated_time: legitResponse.estimated_time
        };
      } else {
        this.log('error', 'Order submission failed', legitResponse);
        
        return {
          success: false,
          status: 'failed',
          message: legitResponse.message,
          error: 'API_ERROR'
        };
      }

    } catch (error) {
      this.log('error', 'Error submitting order', error);
      return this.handleApiError(error);
    }
  }

  async checkStatus(orderId: string): Promise<ApiStatusResponse> {
    try {
      this.log('info', 'Checking order status', { order_id: orderId });

      const response = await this.withRetry(async () => {
        return await this.makeRequest(`/orders/${orderId}/status`, 'GET');
      });

      const statusResponse = response as LegitUnlocksStatusResponse;

      if (statusResponse.success) {
        const result: OrderResult | undefined = statusResponse.result ? {
          success: true,
          message: statusResponse.message,
          unlock_code: statusResponse.result.unlock_code,
          instructions: statusResponse.result.instructions,
          files: statusResponse.result.files?.map(file => ({
            name: file.name,
            url: file.url,
            type: file.type,
            size: 0 // LegitUnlocks doesn't provide file size
          }))
        } : undefined;

        return {
          success: true,
          status: this.mapOrderStatus(statusResponse.status),
          message: statusResponse.message,
          result
        };
      } else {
        return {
          success: false,
          status: 'failed',
          message: statusResponse.message,
          error: 'STATUS_CHECK_FAILED'
        };
      }

    } catch (error) {
      this.log('error', 'Error checking status', error);
      return {
        success: false,
        status: 'failed',
        message: 'Failed to check order status',
        error: error.toString()
      };
    }
  }

  async validateService(serviceId: string, deviceInfo: DeviceInfo): Promise<ServiceValidation> {
    try {
      const services = await this.getAvailableServices();
      const service = services.find(s => s.id === serviceId);

      if (!service) {
        return {
          isValid: false,
          errors: ['Service not found'],
          warnings: []
        };
      }

      const errors: string[] = [];
      const warnings: string[] = [];

      // Check if brand is supported
      if (service.brands && service.brands.length > 0) {
        const brandSupported = service.brands.some(brand => 
          brand.toLowerCase() === deviceInfo.brand.toLowerCase()
        );
        
        if (!brandSupported) {
          errors.push(`Brand ${deviceInfo.brand} is not supported for this service`);
        }
      }

      // Check required fields
      const missingFields = this.validateRequiredFields(deviceInfo, service.requirements || []);
      if (missingFields.length > 0) {
        errors.push(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Validate IMEI if required
      if (service.requirements?.includes('imei') && deviceInfo.imei) {
        if (!this.isValidImei(deviceInfo.imei)) {
          errors.push('Invalid IMEI format');
        }
      }

      // Validate serial number if required
      if (service.requirements?.includes('serial') && deviceInfo.serial_number) {
        if (!this.isValidSerialNumber(deviceInfo.serial_number)) {
          warnings.push('Serial number format may be invalid');
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      this.log('error', 'Error validating service', error);
      return {
        isValid: false,
        errors: ['Service validation failed'],
        warnings: []
      };
    }
  }

  async validateDevice(deviceInfo: DeviceInfo): Promise<DeviceValidation> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const requiredFields = ['brand', 'model'];
    const missingFields = this.validateRequiredFields(deviceInfo, requiredFields);

    if (missingFields.length > 0) {
      errors.push(`Missing required device fields: ${missingFields.join(', ')}`);
    }

    // Additional device-specific validations
    if (deviceInfo.imei && !this.isValidImei(deviceInfo.imei)) {
      errors.push('Invalid IMEI format (must be 15 digits)');
    }

    if (deviceInfo.serial_number && !this.isValidSerialNumber(deviceInfo.serial_number)) {
      warnings.push('Serial number format may be invalid');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      supported_device: true, // Assume supported unless proven otherwise
      required_fields: requiredFields,
      missing_fields: missingFields
    };
  }

  async getAvailableServices(): Promise<LegitUnlocksService[]> {
    try {
      // Check cache first
      const now = Date.now();
      if (this.services.length > 0 && (now - this.lastServicesFetch) < this.servicesCacheTime) {
        return this.services;
      }

      this.log('info', 'Fetching available services from LegitUnlocks');

      const response = await this.withRetry(async () => {
        return await this.makeRequest('/services', 'GET');
      });

      if (response.success && response.services) {
        this.services = response.services;
        this.lastServicesFetch = now;
        
        this.log('info', `Fetched ${this.services.length} services from LegitUnlocks`);
      }

      return this.services;

    } catch (error) {
      this.log('error', 'Error fetching services', error);
      return this.services; // Return cached services if available
    }
  }

  // Override the status mapping for LegitUnlocks specific statuses
  protected mapOrderStatus(providerStatus: string): 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded' {
    const statusMap: Record<string, 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded'> = {
      'pending': 'pending',
      'queued': 'pending',
      'processing': 'processing',
      'in_progress': 'processing',
      'working': 'processing',
      'completed': 'completed',
      'success': 'completed',
      'finished': 'completed',
      'failed': 'failed',
      'error': 'failed',
      'rejected': 'failed',
      'cancelled': 'cancelled',
      'refunded': 'refunded'
    };

    return statusMap[providerStatus.toLowerCase()] || 'pending';
  }

  // LegitUnlocks specific health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/ping', 'GET');
      return response.success === true;
    } catch (error) {
      this.log('error', 'Health check failed', error);
      return false;
    }
  }

  // Get service pricing
  async getServicePricing(serviceId: string): Promise<number | null> {
    try {
      const services = await this.getAvailableServices();
      const service = services.find(s => s.id === serviceId);
      return service?.price || null;
    } catch (error) {
      this.log('error', 'Error getting service pricing', error);
      return null;
    }
  }

  // Get estimated completion time
  async getEstimatedTime(serviceId: string): Promise<number | null> {
    try {
      const services = await this.getAvailableServices();
      const service = services.find(s => s.id === serviceId);
      return service?.time || null;
    } catch (error) {
      this.log('error', 'Error getting estimated time', error);
      return null;
    }
  }
}
