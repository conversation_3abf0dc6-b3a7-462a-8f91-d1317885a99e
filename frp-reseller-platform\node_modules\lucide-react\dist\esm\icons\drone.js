/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 10 7 7", key: "zp14k7" }],
  ["path", { d: "m10 14-3 3", key: "1jrpxk" }],
  ["path", { d: "m14 10 3-3", key: "7tigam" }],
  ["path", { d: "m14 14 3 3", key: "vm23p3" }],
  ["path", { d: "M14.205 4.139a4 4 0 1 1 5.439 5.863", key: "1tm5p2" }],
  ["path", { d: "M19.637 14a4 4 0 1 1-5.432 5.868", key: "16egi2" }],
  ["path", { d: "M4.367 10a4 4 0 1 1 5.438-5.862", key: "1wta6a" }],
  ["path", { d: "M9.795 19.862a4 4 0 1 1-5.429-5.873", key: "q39hpv" }],
  ["rect", { x: "10", y: "8", width: "4", height: "8", rx: "1", key: "phrjt1" }]
];
const Drone = createLucideIcon("drone", __iconNode);

export { __iconNode, Drone as default };
//# sourceMappingURL=drone.js.map
