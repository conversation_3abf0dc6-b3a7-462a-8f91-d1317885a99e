'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { Service } from '@/types/services';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import Button from '@/components/ui/Button';

interface ServiceCardProps {
  service: Service;
  onOrderClick?: (service: Service) => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, onOrderClick }) => {
  const { data: session } = useSession();
  
  const userRole = session?.user?.role || 'client';
  const price = userRole === 'reseller' ? service.reseller_price : service.base_price;
  const savings = userRole === 'reseller' ? service.base_price - service.reseller_price : 0;

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'frp':
        return 'bg-blue-100 text-blue-800';
      case 'imei':
        return 'bg-green-100 text-green-800';
      case 'unlock':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <Card className="h-full hover:shadow-lg transition-shadow duration-200">
      <Card.Content className="p-6">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {service.name}
            </h3>
            <Badge className={getCategoryColor(service.category)}>
              {service.category}
            </Badge>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">
              ${price.toFixed(2)}
            </div>
            {userRole === 'reseller' && savings > 0 && (
              <div className="text-sm text-gray-500 line-through">
                ${service.base_price.toFixed(2)}
              </div>
            )}
            {userRole === 'reseller' && savings > 0 && (
              <div className="text-sm text-green-600 font-medium">
                Ahorras ${savings.toFixed(2)}
              </div>
            )}
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {service.description}
        </p>

        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Tiempo estimado:</span>
            <span className="font-medium text-gray-900">
              {formatTime(service.estimated_time_minutes)}
            </span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Proveedor:</span>
            <span className="font-medium text-gray-900 capitalize">
              {service.api_provider}
            </span>
          </div>
        </div>

        {service.supported_brands && service.supported_brands.length > 0 && (
          <div className="mb-4">
            <div className="text-sm text-gray-500 mb-2">Marcas soportadas:</div>
            <div className="flex flex-wrap gap-1">
              {service.supported_brands.slice(0, 4).map((brand, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {brand}
                </Badge>
              ))}
              {service.supported_brands.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{service.supported_brands.length - 4} más
                </Badge>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          {session ? (
            <Button
              onClick={() => onOrderClick?.(service)}
              className="flex-1"
              disabled={!service.is_active}
            >
              {service.is_active ? 'Solicitar Servicio' : 'No Disponible'}
            </Button>
          ) : (
            <Link href="/auth/login" className="flex-1">
              <Button className="w-full">
                Iniciar Sesión
              </Button>
            </Link>
          )}
          
          <Link href={`/services/${service.id}`}>
            <Button variant="outline">
              Ver Detalles
            </Button>
          </Link>
        </div>
      </Card.Content>
    </Card>
  );
};

export default ServiceCard;
