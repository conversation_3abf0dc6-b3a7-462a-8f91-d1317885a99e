'use client';

import React from 'react';
import Link from 'next/link';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import Button from '@/components/ui/Button';

interface SidebarProps {
  user?: {
    name: string;
    email: string;
    role: string;
    credits: number;
  } | null;
}

const Sidebar: React.FC<SidebarProps> = ({ user }) => {
  const quickStats = [
    { label: 'Órdenes Hoy', value: '12', color: 'info' as const },
    { label: 'Completadas', value: '8', color: 'success' as const },
    { label: 'Pendientes', value: '4', color: 'warning' as const },
  ];

  const popularServices = [
    { name: 'Samsung FRP Bypass', price: '$15', category: 'FRP' },
    { name: 'iPhone IMEI Repair', price: '$25', category: 'IMEI' },
    { name: 'Xiaomi FRP Remove', price: '$12', category: 'FRP' },
    { name: '<PERSON>aw<PERSON> Unlock', price: '$20', category: 'Unlock' },
  ];

  const recentActivity = [
    { action: 'Orden completada', device: 'Samsung A52', time: 'hace 2 min' },
    { action: 'Nueva orden', device: 'iPhone 12', time: 'hace 15 min' },
    { action: 'Pago recibido', device: 'Créditos +$50', time: 'hace 1 hora' },
  ];

  return (
    <aside className="w-80 space-y-4">
      {/* User Info Card */}
      {user && (
        <Card>
          <Card.Header>
            <h3 className="taringa-title text-lg">Mi Cuenta</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              <div>
                <p className="font-medium">{user.name}</p>
                <p className="taringa-meta">{user.email}</p>
                <Badge variant="info" className="mt-1">
                  {user.role === 'admin' ? 'Administrador' : 
                   user.role === 'reseller' ? 'Revendedor' : 'Cliente'}
                </Badge>
              </div>
              <div className="bg-blue-50 p-3 rounded">
                <p className="text-sm font-medium text-blue-900">
                  Créditos Disponibles
                </p>
                <p className="text-2xl font-bold text-blue-600">
                  ${user.credits}
                </p>
              </div>
              <Link href="/credits/purchase">
                <Button variant="orange" size="sm" className="w-full">
                  Comprar Créditos
                </Button>
              </Link>
            </div>
          </Card.Content>
        </Card>
      )}

      {/* Quick Stats */}
      <Card>
        <Card.Header>
          <h3 className="taringa-title text-lg">Estadísticas Rápidas</h3>
        </Card.Header>
        <Card.Content>
          <div className="space-y-3">
            {quickStats.map((stat, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{stat.label}</span>
                <Badge variant={stat.color}>{stat.value}</Badge>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Popular Services */}
      <Card>
        <Card.Header>
          <h3 className="taringa-title text-lg">Servicios Populares</h3>
        </Card.Header>
        <Card.Content>
          <div className="space-y-3">
            {popularServices.map((service, index) => (
              <div key={index} className="border-b border-gray-100 pb-2 last:border-b-0">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium">{service.name}</p>
                    <p className="taringa-meta">{service.category}</p>
                  </div>
                  <span className="text-sm font-bold text-green-600">
                    {service.price}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
        <Card.Footer>
          <Link href="/services">
            <Button variant="outline" size="sm" className="w-full">
              Ver Todos los Servicios
            </Button>
          </Link>
        </Card.Footer>
      </Card>

      {/* Recent Activity */}
      <Card>
        <Card.Header>
          <h3 className="taringa-title text-lg">Actividad Reciente</h3>
        </Card.Header>
        <Card.Content>
          <div className="space-y-3">
            {recentActivity.map((activity, index) => (
              <div key={index} className="border-b border-gray-100 pb-2 last:border-b-0">
                <p className="text-sm font-medium">{activity.action}</p>
                <p className="text-sm text-gray-600">{activity.device}</p>
                <p className="taringa-meta">{activity.time}</p>
              </div>
            ))}
          </div>
        </Card.Content>
        <Card.Footer>
          <Link href="/activity">
            <Button variant="ghost" size="sm" className="w-full">
              Ver Toda la Actividad
            </Button>
          </Link>
        </Card.Footer>
      </Card>

      {/* Support */}
      <Card>
        <Card.Header>
          <h3 className="taringa-title text-lg">Soporte</h3>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2">
            <Link href="/help" className="block text-sm text-blue-600 hover:underline">
              📚 Centro de Ayuda
            </Link>
            <Link href="/contact" className="block text-sm text-blue-600 hover:underline">
              💬 Contactar Soporte
            </Link>
            <Link href="/telegram" className="block text-sm text-blue-600 hover:underline">
              📱 Grupo Telegram
            </Link>
            <Link href="/whatsapp" className="block text-sm text-blue-600 hover:underline">
              📞 WhatsApp
            </Link>
          </div>
        </Card.Content>
      </Card>
    </aside>
  );
};

export default Sidebar;
