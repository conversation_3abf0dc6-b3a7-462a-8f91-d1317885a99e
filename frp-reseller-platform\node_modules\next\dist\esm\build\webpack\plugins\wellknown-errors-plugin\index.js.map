{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/index.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nimport { getModuleBuildError } from './webpackModuleError'\n\nconst NAME = 'WellKnownErrorsPlugin'\nexport class WellKnownErrorsPlugin {\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(NAME, (compilation) => {\n      compilation.hooks.afterSeal.tapPromise(NAME, async () => {\n        if (compilation.warnings?.length) {\n          await Promise.all(\n            compilation.warnings.map(async (warn, i) => {\n              if (\n                warn.name === 'ModuleDependencyWarning' &&\n                warn.module?.context?.includes('node_modules')\n              ) {\n                compilation.warnings.splice(i, 1)\n              }\n            })\n          )\n        }\n\n        if (compilation.errors?.length) {\n          await Promise.all(\n            compilation.errors.map(async (err, i) => {\n              try {\n                const moduleError = await getModuleBuildError(\n                  compiler,\n                  compilation,\n                  err\n                )\n                if (moduleError !== false) {\n                  compilation.errors[i] = moduleError\n                }\n              } catch (e) {\n                console.log(e)\n              }\n            })\n          )\n        }\n      })\n    })\n  }\n}\n"], "names": ["getModuleBuildError", "NAME", "WellKnownErrorsPlugin", "apply", "compiler", "hooks", "compilation", "tap", "afterSeal", "tapPromise", "warnings", "length", "Promise", "all", "map", "warn", "i", "name", "module", "context", "includes", "splice", "errors", "err", "moduleError", "e", "console", "log"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,uBAAsB;AAE1D,MAAMC,OAAO;AACb,OAAO,MAAMC;IACXC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACN,MAAM,CAACK;YACpCA,YAAYD,KAAK,CAACG,SAAS,CAACC,UAAU,CAACR,MAAM;oBACvCK,uBAaAA;gBAbJ,KAAIA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,MAAM,EAAE;oBAChC,MAAMC,QAAQC,GAAG,CACfP,YAAYI,QAAQ,CAACI,GAAG,CAAC,OAAOC,MAAMC;4BAGlCD,sBAAAA;wBAFF,IACEA,KAAKE,IAAI,KAAK,+BACdF,eAAAA,KAAKG,MAAM,sBAAXH,uBAAAA,aAAaI,OAAO,qBAApBJ,qBAAsBK,QAAQ,CAAC,kBAC/B;4BACAd,YAAYI,QAAQ,CAACW,MAAM,CAACL,GAAG;wBACjC;oBACF;gBAEJ;gBAEA,KAAIV,sBAAAA,YAAYgB,MAAM,qBAAlBhB,oBAAoBK,MAAM,EAAE;oBAC9B,MAAMC,QAAQC,GAAG,CACfP,YAAYgB,MAAM,CAACR,GAAG,CAAC,OAAOS,KAAKP;wBACjC,IAAI;4BACF,MAAMQ,cAAc,MAAMxB,oBACxBI,UACAE,aACAiB;4BAEF,IAAIC,gBAAgB,OAAO;gCACzBlB,YAAYgB,MAAM,CAACN,EAAE,GAAGQ;4BAC1B;wBACF,EAAE,OAAOC,GAAG;4BACVC,QAAQC,GAAG,CAACF;wBACd;oBACF;gBAEJ;YACF;QACF;IACF;AACF", "ignoreList": [0]}