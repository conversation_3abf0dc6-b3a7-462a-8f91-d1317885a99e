{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { createClient } from '@supabase/supabase-js';\nimport bcrypt from 'bcryptjs';\nimport type { User, UserRole } from '@/types';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Get user from Supabase\n          const { data: user, error } = await supabase\n            .from('users')\n            .select(`\n              *,\n              user_profiles (\n                first_name,\n                last_name,\n                phone,\n                country,\n                role,\n                is_active,\n                credits\n              )\n            `)\n            .eq('email', credentials.email)\n            .single();\n\n          if (error || !user) {\n            return null;\n          }\n\n          // Verify password\n          const isValidPassword = await bcrypt.compare(\n            credentials.password,\n            user.password_hash\n          );\n\n          if (!isValidPassword) {\n            return null;\n          }\n\n          // Check if user is active\n          if (!user.user_profiles?.is_active) {\n            return null;\n          }\n\n          return {\n            id: user.id,\n            email: user.email,\n            name: `${user.user_profiles.first_name} ${user.user_profiles.last_name}`,\n            role: user.user_profiles.role as UserRole,\n            credits: user.user_profiles.credits,\n            isActive: user.user_profiles.is_active,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.credits = user.credits;\n        token.isActive = user.isActive;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (session.user) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as UserRole;\n        session.user.credits = token.credits as number;\n        session.user.isActive = token.isActive as boolean;\n      }\n      return session;\n    }\n  },\n  pages: {\n    signIn: '/auth/login',\n    signUp: '/auth/register',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n// Helper function to hash passwords\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\n// Helper function to create a new user\nexport async function createUser(userData: {\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  country?: string;\n  role?: UserRole;\n}) {\n  const hashedPassword = await hashPassword(userData.password);\n\n  try {\n    // Create user in auth table\n    const { data: user, error: userError } = await supabase\n      .from('users')\n      .insert({\n        email: userData.email,\n        password_hash: hashedPassword,\n      })\n      .select()\n      .single();\n\n    if (userError) {\n      throw userError;\n    }\n\n    // Create user profile\n    const { data: profile, error: profileError } = await supabase\n      .from('user_profiles')\n      .insert({\n        user_id: user.id,\n        first_name: userData.firstName,\n        last_name: userData.lastName,\n        phone: userData.phone,\n        country: userData.country || 'Argentina',\n        role: userData.role || 'client',\n        is_active: true,\n        credits: 0,\n      })\n      .select()\n      .single();\n\n    if (profileError) {\n      // Rollback user creation if profile creation fails\n      await supabase.from('users').delete().eq('id', user.id);\n      throw profileError;\n    }\n\n    return {\n      user,\n      profile\n    };\n  } catch (error) {\n    console.error('Error creating user:', error);\n    throw error;\n  }\n}\n\n// Helper function to get user by email\nexport async function getUserByEmail(email: string) {\n  const { data, error } = await supabase\n    .from('users')\n    .select(`\n      *,\n      user_profiles (\n        first_name,\n        last_name,\n        phone,\n        country,\n        role,\n        is_active,\n        credits\n      )\n    `)\n    .eq('email', email)\n    .single();\n\n  if (error) {\n    return null;\n  }\n\n  return data;\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAGA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,wEAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,yBAAyB;oBACzB,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;YAWT,CAAC,EACA,EAAE,CAAC,SAAS,YAAY,KAAK,EAC7B,MAAM;oBAET,IAAI,SAAS,CAAC,MAAM;wBAClB,OAAO;oBACT;oBAEA,kBAAkB;oBAClB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,aAAa;oBAGpB,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,0BAA0B;oBAC1B,IAAI,CAAC,KAAK,aAAa,EAAE,WAAW;wBAClC,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,aAAa,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,SAAS,EAAE;wBACxE,MAAM,KAAK,aAAa,CAAC,IAAI;wBAC7B,SAAS,KAAK,aAAa,CAAC,OAAO;wBACnC,UAAU,KAAK,aAAa,CAAC,SAAS;oBACxC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,OAAO,GAAG,KAAK,OAAO;gBAC5B,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;gBACpC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,WAAW,QAQhC;IACC,MAAM,iBAAiB,MAAM,aAAa,SAAS,QAAQ;IAE3D,IAAI;QACF,4BAA4B;QAC5B,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC;YACN,OAAO,SAAS,KAAK;YACrB,eAAe;QACjB,GACC,MAAM,GACN,MAAM;QAET,IAAI,WAAW;YACb,MAAM;QACR;QAEA,sBAAsB;QACtB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,SAAS,KAAK,EAAE;YAChB,YAAY,SAAS,SAAS;YAC9B,WAAW,SAAS,QAAQ;YAC5B,OAAO,SAAS,KAAK;YACrB,SAAS,SAAS,OAAO,IAAI;YAC7B,MAAM,SAAS,IAAI,IAAI;YACvB,WAAW;YACX,SAAS;QACX,GACC,MAAM,GACN,MAAM;QAET,IAAI,cAAc;YAChB,mDAAmD;YACnD,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM,KAAK,EAAE;YACtD,MAAM;QACR;QAEA,OAAO;YACL;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,eAAe,eAAe,KAAa;IAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;IAWT,CAAC,EACA,EAAE,CAAC,SAAS,OACZ,MAAM;IAET,IAAI,OAAO;QACT,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}