// Service-related types for the FRP Reseller Platform

export interface Service {
  id: string;
  name: string;
  description: string;
  category: ServiceCategory;
  base_price: number;
  reseller_price: number;
  estimated_time_minutes: number;
  is_active: boolean;
  api_provider: string;
  api_service_id?: string;
  requirements: string[];
  supported_brands: string[];
  created_at: string;
  updated_at: string;
}

export type ServiceCategory = 'FRP' | 'IMEI' | 'Unlock' | 'Other';

export interface ServiceRequest {
  service_id: string;
  device_info: DeviceInfo;
  user_notes?: string;
}

export interface DeviceInfo {
  brand: string;
  model: string;
  imei?: string;
  serial_number?: string;
  android_version?: string;
  ios_version?: string;
  miui_version?: string;
  emui_version?: string;
  additional_info?: Record<string, any>;
}

export interface Order {
  id: string;
  order_number: string;
  user_id: string;
  service_id: string;
  service?: Service;
  status: OrderStatus;
  device_info: DeviceInfo;
  price_paid: number;
  reseller_commission: number;
  api_order_id?: string;
  api_response?: Record<string, any>;
  result_data?: OrderResult;
  notes?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export type OrderStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled' 
  | 'refunded';

export interface OrderResult {
  success: boolean;
  message: string;
  unlock_code?: string;
  instructions?: string;
  files?: FileResult[];
  additional_data?: Record<string, any>;
}

export interface FileResult {
  name: string;
  url: string;
  type: string;
  size: number;
}

export interface ApiProvider {
  name: string;
  displayName: string;
  baseUrl: string;
  apiKey: string;
  isActive: boolean;
  configuration: Record<string, any>;
}

export interface ApiServiceMapping {
  local_service_id: string;
  provider_service_id: string;
  provider_name: string;
  price_multiplier?: number;
}

// API Request/Response interfaces
export interface ApiServiceRequest {
  service_id: string;
  device_info: DeviceInfo;
  callback_url?: string;
  reference_id?: string;
}

export interface ApiServiceResponse {
  success: boolean;
  order_id?: string;
  status: string;
  message: string;
  estimated_time?: number;
  result?: OrderResult;
  error?: string;
}

export interface ApiStatusResponse {
  success: boolean;
  status: OrderStatus;
  message: string;
  result?: OrderResult;
  error?: string;
}

// Service validation interfaces
export interface ServiceValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface DeviceValidation extends ServiceValidation {
  supported_device: boolean;
  required_fields: string[];
  missing_fields: string[];
}

// Service statistics
export interface ServiceStats {
  total_orders: number;
  completed_orders: number;
  failed_orders: number;
  success_rate: number;
  average_completion_time: number;
  revenue: number;
}

export interface ProviderStats {
  provider_name: string;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_response_time: number;
  uptime_percentage: number;
  last_request_at?: string;
}

// Credit and pricing
export interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  currency: string;
  bonus_credits: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  order_id?: string;
  type: TransactionType;
  amount: number;
  currency: string;
  status: TransactionStatus;
  payment_method?: string;
  payment_id?: string;
  payment_data?: Record<string, any>;
  description: string;
  created_at: string;
  updated_at: string;
}

export type TransactionType = 
  | 'credit_purchase' 
  | 'service_payment' 
  | 'refund' 
  | 'commission';

export type TransactionStatus = 
  | 'pending' 
  | 'completed' 
  | 'failed' 
  | 'cancelled';

// Form interfaces
export interface ServiceOrderForm {
  service_id: string;
  brand: string;
  model: string;
  imei?: string;
  serial_number?: string;
  android_version?: string;
  ios_version?: string;
  additional_notes?: string;
}

export interface ServiceSearchFilters {
  category?: ServiceCategory;
  brand?: string;
  price_min?: number;
  price_max?: number;
  search_term?: string;
  is_active?: boolean;
}

// API Configuration
export interface ApiConfiguration {
  id: string;
  provider_name: string;
  api_url: string;
  api_key: string;
  is_active: boolean;
  configuration: Record<string, any>;
  created_at: string;
  updated_at: string;
}
