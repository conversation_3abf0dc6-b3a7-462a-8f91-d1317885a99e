'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

interface HeaderProps {
  user?: {
    name: string;
    email: string;
    role: string;
    credits: number;
  } | null;
}

const Header: React.FC<HeaderProps> = ({ user }) => {
  const pathname = usePathname();

  const navigation = [
    { name: 'Inicio', href: '/', current: pathname === '/' },
    { name: 'Servicios', href: '/services', current: pathname === '/services' },
    { name: '<PERSON><PERSON>', href: '/orders', current: pathname === '/orders' },
    { name: 'Precios', href: '/pricing', current: pathname === '/pricing' },
  ];

  const adminNavigation = [
    { name: 'Admin', href: '/admin', current: pathname.startsWith('/admin') },
  ];

  const resellerNavigation = [
    { name: 'Panel Revendedor', href: '/reseller', current: pathname.startsWith('/reseller') },
  ];

  return (
    <header className="taringa-header">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-bold text-lg">F</span>
              </div>
              <span className="text-white font-bold text-xl">FRP Reseller</span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  item.current
                    ? 'bg-blue-700 text-white'
                    : 'text-blue-100 hover:bg-blue-700 hover:text-white'
                )}
              >
                {item.name}
              </Link>
            ))}
            
            {user?.role === 'admin' && adminNavigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  item.current
                    ? 'bg-orange-600 text-white'
                    : 'text-orange-200 hover:bg-orange-600 hover:text-white'
                )}
              >
                {item.name}
              </Link>
            ))}

            {user?.role === 'reseller' && resellerNavigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  item.current
                    ? 'bg-orange-600 text-white'
                    : 'text-orange-200 hover:bg-orange-600 hover:text-white'
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* User menu */}
          <div className="flex items-center space-x-4">
            {user ? (
              <>
                <div className="text-white text-sm">
                  <span className="font-medium">{user.name}</span>
                  <div className="text-blue-200 text-xs">
                    Créditos: ${user.credits}
                  </div>
                </div>
                <Button
                  variant="orange"
                  size="sm"
                  onClick={() => {/* TODO: Implement logout */}}
                >
                  Salir
                </Button>
              </>
            ) : (
              <div className="flex space-x-2">
                <Link href="/auth/login">
                  <Button variant="outline" size="sm" className="text-white border-white hover:bg-white hover:text-blue-600">
                    Iniciar Sesión
                  </Button>
                </Link>
                <Link href="/auth/register">
                  <Button variant="orange" size="sm">
                    Registrarse
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="md:hidden">
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-800">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'block px-3 py-2 rounded-md text-base font-medium transition-colors',
                item.current
                  ? 'bg-blue-700 text-white'
                  : 'text-blue-100 hover:bg-blue-700 hover:text-white'
              )}
            >
              {item.name}
            </Link>
          ))}
        </div>
      </div>
    </header>
  );
};

export default Header;
