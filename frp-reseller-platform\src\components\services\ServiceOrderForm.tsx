'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Service, ServiceOrderForm as ServiceOrderFormType } from '@/types/services';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Badge from '@/components/ui/Badge';

interface ServiceOrderFormProps {
  service: Service;
  onSubmit: (formData: ServiceOrderFormType) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const ServiceOrderForm: React.FC<ServiceOrderFormProps> = ({
  service,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const { data: session } = useSession();
  const [formData, setFormData] = useState<ServiceOrderFormType>({
    service_id: service.id,
    brand: '',
    model: '',
    imei: '',
    serial_number: '',
    android_version: '',
    ios_version: '',
    additional_notes: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const userRole = session?.user?.role || 'client';
  const price = userRole === 'reseller' ? service.reseller_price : service.base_price;
  const userCredits = session?.user?.credits || 0;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.brand.trim()) {
      newErrors.brand = 'La marca es requerida';
    }

    if (!formData.model.trim()) {
      newErrors.model = 'El modelo es requerido';
    }

    // Check service-specific requirements
    if (service.requirements?.includes('imei') && !formData.imei?.trim()) {
      newErrors.imei = 'El IMEI es requerido para este servicio';
    }

    if (service.requirements?.includes('serial') && !formData.serial_number?.trim()) {
      newErrors.serial_number = 'El número de serie es requerido para este servicio';
    }

    // Validate IMEI format if provided
    if (formData.imei && !/^\d{15}$/.test(formData.imei)) {
      newErrors.imei = 'El IMEI debe tener exactamente 15 dígitos';
    }

    // Check if brand is supported
    if (service.supported_brands?.length > 0 && formData.brand) {
      const brandSupported = service.supported_brands.some(
        brand => brand.toLowerCase() === formData.brand.toLowerCase()
      );
      if (!brandSupported) {
        newErrors.brand = `La marca ${formData.brand} no está soportada para este servicio`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (userCredits < price) {
      setErrors({ general: 'Créditos insuficientes para realizar esta orden' });
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error: any) {
      setErrors({ general: error.message || 'Error al crear la orden' });
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'frp':
        return 'bg-blue-100 text-blue-800';
      case 'imei':
        return 'bg-green-100 text-green-800';
      case 'unlock':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <Card.Header>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Solicitar Servicio
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Completa la información del dispositivo
              </p>
            </div>
            <Badge className={getCategoryColor(service.category)}>
              {service.category}
            </Badge>
          </div>
        </Card.Header>

        <Card.Content>
          {/* Service Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-2">{service.name}</h3>
            <p className="text-sm text-gray-600 mb-3">{service.description}</p>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Precio:</span>
                <span className="font-semibold text-blue-600 ml-2">
                  ${price.toFixed(2)}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Tiempo estimado:</span>
                <span className="font-medium ml-2">
                  {service.estimated_time_minutes} min
                </span>
              </div>
            </div>

            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-500">Tus créditos:</span>
                <span className={`font-medium ${userCredits >= price ? 'text-green-600' : 'text-red-600'}`}>
                  ${userCredits.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Order Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {errors.general && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {errors.general}
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-1">
                  Marca *
                </label>
                <Input
                  id="brand"
                  name="brand"
                  value={formData.brand}
                  onChange={handleChange}
                  placeholder="Samsung, Apple, Xiaomi..."
                  disabled={isLoading}
                  className={errors.brand ? 'border-red-300' : ''}
                />
                {errors.brand && (
                  <p className="text-red-600 text-xs mt-1">{errors.brand}</p>
                )}
              </div>

              <div>
                <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                  Modelo *
                </label>
                <Input
                  id="model"
                  name="model"
                  value={formData.model}
                  onChange={handleChange}
                  placeholder="Galaxy A52, iPhone 12..."
                  disabled={isLoading}
                  className={errors.model ? 'border-red-300' : ''}
                />
                {errors.model && (
                  <p className="text-red-600 text-xs mt-1">{errors.model}</p>
                )}
              </div>
            </div>

            {service.requirements?.includes('imei') && (
              <div>
                <label htmlFor="imei" className="block text-sm font-medium text-gray-700 mb-1">
                  IMEI *
                </label>
                <Input
                  id="imei"
                  name="imei"
                  value={formData.imei}
                  onChange={handleChange}
                  placeholder="123456789012345"
                  maxLength={15}
                  disabled={isLoading}
                  className={errors.imei ? 'border-red-300' : ''}
                />
                {errors.imei && (
                  <p className="text-red-600 text-xs mt-1">{errors.imei}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Ingresa el IMEI de 15 dígitos (sin espacios ni guiones)
                </p>
              </div>
            )}

            {service.requirements?.includes('serial') && (
              <div>
                <label htmlFor="serial_number" className="block text-sm font-medium text-gray-700 mb-1">
                  Número de Serie *
                </label>
                <Input
                  id="serial_number"
                  name="serial_number"
                  value={formData.serial_number}
                  onChange={handleChange}
                  placeholder="ABC123DEF456"
                  disabled={isLoading}
                  className={errors.serial_number ? 'border-red-300' : ''}
                />
                {errors.serial_number && (
                  <p className="text-red-600 text-xs mt-1">{errors.serial_number}</p>
                )}
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="android_version" className="block text-sm font-medium text-gray-700 mb-1">
                  Versión Android
                </label>
                <Input
                  id="android_version"
                  name="android_version"
                  value={formData.android_version}
                  onChange={handleChange}
                  placeholder="11, 12, 13..."
                  disabled={isLoading}
                />
              </div>

              <div>
                <label htmlFor="ios_version" className="block text-sm font-medium text-gray-700 mb-1">
                  Versión iOS
                </label>
                <Input
                  id="ios_version"
                  name="ios_version"
                  value={formData.ios_version}
                  onChange={handleChange}
                  placeholder="15.7, 16.1..."
                  disabled={isLoading}
                />
              </div>
            </div>

            <div>
              <label htmlFor="additional_notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notas adicionales
              </label>
              <textarea
                id="additional_notes"
                name="additional_notes"
                value={formData.additional_notes}
                onChange={handleChange}
                rows={3}
                className="taringa-input w-full"
                placeholder="Información adicional sobre el dispositivo o el problema..."
                disabled={isLoading}
              />
            </div>

            {service.supported_brands && service.supported_brands.length > 0 && (
              <div className="bg-blue-50 rounded-lg p-3">
                <p className="text-sm font-medium text-blue-900 mb-2">
                  Marcas soportadas:
                </p>
                <div className="flex flex-wrap gap-1">
                  {service.supported_brands.map((brand, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {brand}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading || userCredits < price}
                className="flex-1"
              >
                {isLoading ? 'Procesando...' : `Solicitar Servicio ($${price.toFixed(2)})`}
              </Button>
            </div>
          </form>
        </Card.Content>
      </Card>
    </div>
  );
};

export default ServiceOrderForm;
