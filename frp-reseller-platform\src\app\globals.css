@import "tailwindcss";

/* Taringa-inspired color scheme */
:root {
  --taringa-blue: #0066cc;
  --taringa-light-blue: #e6f2ff;
  --taringa-dark-blue: #004499;
  --taringa-orange: #ff6600;
  --taringa-light-orange: #fff2e6;
  --taringa-gray: #f5f5f5;
  --taringa-dark-gray: #333333;
  --taringa-border: #cccccc;
  --taringa-text: #333333;
  --taringa-light-text: #666666;
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: 'Arial', 'Helvetica', sans-serif;
  background-color: #ffffff;
  color: var(--taringa-text);
  line-height: 1.4;
}

/* Taringa-style components */
.taringa-header {
  background: linear-gradient(to bottom, #0066cc, #004499);
  border-bottom: 3px solid var(--taringa-orange);
}

.taringa-sidebar {
  background-color: var(--taringa-gray);
  border: 1px solid var(--taringa-border);
}

.taringa-post {
  background-color: white;
  border: 1px solid var(--taringa-border);
  border-radius: 3px;
}

.taringa-button {
  background: linear-gradient(to bottom, #0066cc, #004499);
  color: white;
  border: 1px solid #004499;
  border-radius: 3px;
  font-weight: bold;
  text-shadow: 0 1px 1px rgba(0,0,0,0.3);
}

.taringa-button:hover {
  background: linear-gradient(to bottom, #0077dd, #0055aa);
}

.taringa-button-orange {
  background: linear-gradient(to bottom, #ff6600, #cc5500);
  border: 1px solid #cc5500;
}

.taringa-button-orange:hover {
  background: linear-gradient(to bottom, #ff7711, #dd5500);
}

.taringa-input {
  border: 1px solid var(--taringa-border);
  border-radius: 3px;
  padding: 6px 8px;
  font-size: 13px;
}

.taringa-input:focus {
  border-color: var(--taringa-blue);
  outline: none;
  box-shadow: 0 0 3px rgba(0, 102, 204, 0.3);
}

/* Typography */
.taringa-title {
  color: var(--taringa-blue);
  font-weight: bold;
  text-decoration: none;
}

.taringa-title:hover {
  color: var(--taringa-dark-blue);
  text-decoration: underline;
}

.taringa-meta {
  color: var(--taringa-light-text);
  font-size: 11px;
}

/* Status badges */
.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
