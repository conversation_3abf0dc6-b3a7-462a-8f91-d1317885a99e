import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ServiceManager } from '@/lib/services/manager';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

const serviceManager = new ServiceManager();
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET /api/orders - Get user orders
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const offset = (page - 1) * limit;

    let query = supabase
      .from('orders')
      .select(`
        *,
        services (
          id,
          name,
          category,
          description
        )
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status) {
      query = query.eq('status', status);
    }

    const { data: orders, error, count } = await query;

    if (error) {
      console.error('Error fetching orders:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      orders: orders || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in GET /api/orders:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create new order
const createOrderSchema = z.object({
  service_id: z.string().uuid('Invalid service ID'),
  device_info: z.object({
    brand: z.string().min(1, 'Brand is required'),
    model: z.string().min(1, 'Model is required'),
    imei: z.string().optional(),
    serial_number: z.string().optional(),
    android_version: z.string().optional(),
    ios_version: z.string().optional(),
    miui_version: z.string().optional(),
    emui_version: z.string().optional(),
    additional_info: z.record(z.any()).optional()
  }),
  user_notes: z.string().optional()
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { service_id, device_info, user_notes } = createOrderSchema.parse(body);

    // Submit order through service manager
    const result = await serviceManager.submitOrder(
      service_id,
      device_info,
      session.user.id,
      session.user.role
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    // Add user notes if provided
    if (user_notes && result.order) {
      await supabase
        .from('orders')
        .update({ notes: user_notes })
        .eq('id', result.order.id);
    }

    return NextResponse.json({
      success: true,
      order: result.order,
      message: 'Order created successfully'
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating order:', error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
