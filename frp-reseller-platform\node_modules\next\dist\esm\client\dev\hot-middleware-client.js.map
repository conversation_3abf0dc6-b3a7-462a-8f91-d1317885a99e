{"version": 3, "sources": ["../../../src/client/dev/hot-middleware-client.ts"], "sourcesContent": ["import type {\n  NextRouter,\n  PrivateRouteInfo,\n} from '../../shared/lib/router/router'\nimport connect from './hot-reloader/pages/hot-reloader-pages'\nimport { sendMessage } from './hot-reloader/pages/websocket'\n\n// Define a local type for the window.next object\ninterface NextWindow {\n  next?: {\n    router?: NextRouter & {\n      components: { [pathname: string]: PrivateRouteInfo }\n    }\n  }\n  __nextDevClientId?: string\n  location: Location\n}\n\ndeclare const window: NextWindow\n\nlet reloading = false\n\nexport default () => {\n  const devClient = connect()\n\n  devClient.subscribeToHmrEvent((obj: any) => {\n    if (reloading) return\n\n    // Retrieve the router if it's available\n    const router = window.next?.router\n\n    // Determine if we're on an error page or the router is not initialized\n    const isOnErrorPage =\n      !router || router.pathname === '/404' || router.pathname === '/_error'\n\n    switch (obj.action) {\n      case 'reloadPage': {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-reload-page',\n            clientId: window.__nextDevClientId,\n          })\n        )\n        reloading = true\n        return window.location.reload()\n      }\n      case 'removedPage': {\n        const [page] = obj.data\n\n        // Check if the removed page is the current page\n        const isCurrentPage = page === router?.pathname\n\n        // We enter here if the removed page is currently being viewed\n        // or if we happen to be on an error page.\n        if (isCurrentPage || isOnErrorPage) {\n          sendMessage(\n            JSON.stringify({\n              event: 'client-removed-page',\n              clientId: window.__nextDevClientId,\n              page,\n            })\n          )\n          return window.location.reload()\n        }\n        return\n      }\n      case 'addedPage': {\n        const [page] = obj.data\n\n        // Check if the added page is the current page\n        const isCurrentPage = page === router?.pathname\n\n        // Check if the page component is not yet loaded\n        const isPageNotLoaded =\n          typeof router?.components?.[page] === 'undefined'\n\n        // We enter this block if the newly added page is the one currently being viewed\n        // but hasn't been loaded yet, or if we're on an error page.\n        if ((isCurrentPage && isPageNotLoaded) || isOnErrorPage) {\n          sendMessage(\n            JSON.stringify({\n              event: 'client-added-page',\n              clientId: window.__nextDevClientId,\n              page,\n            })\n          )\n          return window.location.reload()\n        }\n        return\n      }\n      case 'serverError':\n      case 'devPagesManifestUpdate':\n      case 'isrManifest':\n      case 'building':\n      case 'finishBuilding': {\n        return\n      }\n      default: {\n        throw new Error('Unexpected action ' + obj.action)\n      }\n    }\n  })\n\n  return devClient\n}\n"], "names": ["connect", "sendMessage", "reloading", "devClient", "subscribeToHmrEvent", "obj", "window", "router", "next", "isOnErrorPage", "pathname", "action", "JSON", "stringify", "event", "clientId", "__nextDevClientId", "location", "reload", "page", "data", "isCurrentPage", "isPageNotLoaded", "components", "Error"], "mappings": "AAIA,OAAOA,aAAa,0CAAyC;AAC7D,SAASC,WAAW,QAAQ,iCAAgC;AAe5D,IAAIC,YAAY;AAEhB,eAAe,CAAA;IACb,MAAMC,YAAYH;IAElBG,UAAUC,mBAAmB,CAAC,CAACC;YAIdC;QAHf,IAAIJ,WAAW;QAEf,wCAAwC;QACxC,MAAMK,UAASD,eAAAA,OAAOE,IAAI,qBAAXF,aAAaC,MAAM;QAElC,uEAAuE;QACvE,MAAME,gBACJ,CAACF,UAAUA,OAAOG,QAAQ,KAAK,UAAUH,OAAOG,QAAQ,KAAK;QAE/D,OAAQL,IAAIM,MAAM;YAChB,KAAK;gBAAc;oBACjBV,YACEW,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,UAAUT,OAAOU,iBAAiB;oBACpC;oBAEFd,YAAY;oBACZ,OAAOI,OAAOW,QAAQ,CAACC,MAAM;gBAC/B;YACA,KAAK;gBAAe;oBAClB,MAAM,CAACC,KAAK,GAAGd,IAAIe,IAAI;oBAEvB,gDAAgD;oBAChD,MAAMC,gBAAgBF,UAASZ,0BAAAA,OAAQG,QAAQ;oBAE/C,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIW,iBAAiBZ,eAAe;wBAClCR,YACEW,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUT,OAAOU,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOb,OAAOW,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;gBAAa;wBAQPX;oBAPT,MAAM,CAACY,KAAK,GAAGd,IAAIe,IAAI;oBAEvB,8CAA8C;oBAC9C,MAAMC,gBAAgBF,UAASZ,0BAAAA,OAAQG,QAAQ;oBAE/C,gDAAgD;oBAChD,MAAMY,kBACJ,QAAOf,2BAAAA,qBAAAA,OAAQgB,UAAU,qBAAlBhB,kBAAoB,CAACY,KAAK,MAAK;oBAExC,gFAAgF;oBAChF,4DAA4D;oBAC5D,IAAI,AAACE,iBAAiBC,mBAAoBb,eAAe;wBACvDR,YACEW,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUT,OAAOU,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOb,OAAOW,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAkB;oBACrB;gBACF;YACA;gBAAS;oBACP,MAAM,qBAA4C,CAA5C,IAAIM,MAAM,uBAAuBnB,IAAIM,MAAM,GAA3C,qBAAA;+BAAA;oCAAA;sCAAA;oBAA2C;gBACnD;QACF;IACF;IAEA,OAAOR;AACT,CAAA,EAAC", "ignoreList": [0]}