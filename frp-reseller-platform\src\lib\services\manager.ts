// Service Manager - Coordinates all API providers and service operations

import { BaseApiProvider } from './providers/base';
import { LegitUnlocksProvider } from './providers/legitunlocks';
import { 
  Service, 
  Order, 
  ApiServiceRequest, 
  ApiServiceResponse, 
  ApiStatusResponse,
  ServiceValidation,
  DeviceValidation,
  ServiceStats,
  ProviderStats
} from '@/types/services';
import { createClient } from '@supabase/supabase-js';

export class ServiceManager {
  private providers: Map<string, BaseApiProvider> = new Map();
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    this.initializeProviders();
  }

  private async initializeProviders(): Promise<void> {
    try {
      // Load API configurations from database
      const { data: configs, error } = await this.supabase
        .from('api_configurations')
        .select('*')
        .eq('is_active', true);

      if (error) {
        console.error('Error loading API configurations:', error);
        return;
      }

      // Initialize providers based on configurations
      for (const config of configs || []) {
        await this.addProvider(config.provider_name, config);
      }

      console.log(`Initialized ${this.providers.size} API providers`);
    } catch (error) {
      console.error('Error initializing providers:', error);
    }
  }

  private async addProvider(providerName: string, config: any): Promise<void> {
    try {
      let provider: BaseApiProvider;

      switch (providerName.toLowerCase()) {
        case 'legitunlocks':
          provider = new LegitUnlocksProvider(config.api_key, config.configuration);
          break;
        
        // Add more providers here as needed
        default:
          console.warn(`Unknown provider: ${providerName}`);
          return;
      }

      // Test provider health before adding
      const isHealthy = await provider.healthCheck();
      if (isHealthy) {
        this.providers.set(providerName, provider);
        console.log(`Added provider: ${providerName}`);
      } else {
        console.warn(`Provider ${providerName} failed health check`);
      }
    } catch (error) {
      console.error(`Error adding provider ${providerName}:`, error);
    }
  }

  // Submit a service order
  async submitOrder(
    serviceId: string, 
    deviceInfo: any, 
    userId: string,
    userRole: string = 'client'
  ): Promise<{ success: boolean; order?: Order; error?: string }> {
    try {
      // Get service details from database
      const { data: service, error: serviceError } = await this.supabase
        .from('services')
        .select('*')
        .eq('id', serviceId)
        .eq('is_active', true)
        .single();

      if (serviceError || !service) {
        return { success: false, error: 'Service not found or inactive' };
      }

      // Check user credits
      const { data: userProfile, error: userError } = await this.supabase
        .from('user_profiles')
        .select('credits, role')
        .eq('user_id', userId)
        .single();

      if (userError || !userProfile) {
        return { success: false, error: 'User profile not found' };
      }

      // Calculate price based on user role
      const price = userProfile.role === 'reseller' ? service.reseller_price : service.base_price;
      
      if (userProfile.credits < price) {
        return { success: false, error: 'Insufficient credits' };
      }

      // Get the appropriate provider
      const provider = this.providers.get(service.api_provider);
      if (!provider) {
        return { success: false, error: 'Service provider not available' };
      }

      // Validate the service request
      const validation = await provider.validateService(service.api_service_id || serviceId, deviceInfo);
      if (!validation.isValid) {
        return { success: false, error: `Validation failed: ${validation.errors.join(', ')}` };
      }

      // Create order in database
      const { data: order, error: orderError } = await this.supabase
        .from('orders')
        .insert({
          user_id: userId,
          service_id: serviceId,
          status: 'pending',
          device_info: deviceInfo,
          price_paid: price,
          reseller_commission: userProfile.role === 'reseller' ? 
            (service.base_price - service.reseller_price) : 0
        })
        .select()
        .single();

      if (orderError || !order) {
        return { success: false, error: 'Failed to create order' };
      }

      // Submit to API provider
      const apiRequest: ApiServiceRequest = {
        service_id: service.api_service_id || serviceId,
        device_info: deviceInfo,
        reference_id: order.id,
        callback_url: `${process.env.NEXTAUTH_URL}/api/webhooks/${service.api_provider}`
      };

      const apiResponse = await provider.submitOrder(apiRequest);

      // Update order with API response
      const updateData: any = {
        api_order_id: apiResponse.order_id,
        api_response: apiResponse,
        status: apiResponse.success ? apiResponse.status : 'failed'
      };

      if (apiResponse.success && apiResponse.status === 'processing') {
        updateData.started_at = new Date().toISOString();
      }

      const { data: updatedOrder, error: updateError } = await this.supabase
        .from('orders')
        .update(updateData)
        .eq('id', order.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating order:', updateError);
      }

      // Deduct credits if order was submitted successfully
      if (apiResponse.success) {
        await this.supabase
          .from('user_profiles')
          .update({ credits: userProfile.credits - price })
          .eq('user_id', userId);

        // Create transaction record
        await this.supabase
          .from('transactions')
          .insert({
            user_id: userId,
            order_id: order.id,
            type: 'service_payment',
            amount: price,
            status: 'completed',
            payment_method: 'credits',
            description: `Payment for ${service.name}`
          });
      }

      return { 
        success: apiResponse.success, 
        order: updatedOrder || order,
        error: apiResponse.success ? undefined : apiResponse.message 
      };

    } catch (error) {
      console.error('Error submitting order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  // Check order status
  async checkOrderStatus(orderId: string): Promise<{ success: boolean; order?: Order; error?: string }> {
    try {
      // Get order from database
      const { data: order, error: orderError } = await this.supabase
        .from('orders')
        .select(`
          *,
          services (*)
        `)
        .eq('id', orderId)
        .single();

      if (orderError || !order) {
        return { success: false, error: 'Order not found' };
      }

      // If order is already completed or failed, return current status
      if (['completed', 'failed', 'cancelled', 'refunded'].includes(order.status)) {
        return { success: true, order };
      }

      // Check with API provider if we have an API order ID
      if (order.api_order_id && order.services?.api_provider) {
        const provider = this.providers.get(order.services.api_provider);
        if (provider) {
          const statusResponse = await provider.checkStatus(order.api_order_id);
          
          // Update order if status changed
          if (statusResponse.success && statusResponse.status !== order.status) {
            const updateData: any = {
              status: statusResponse.status,
              api_response: { ...order.api_response, ...statusResponse }
            };

            if (statusResponse.result) {
              updateData.result_data = statusResponse.result;
            }

            if (statusResponse.status === 'completed') {
              updateData.completed_at = new Date().toISOString();
            }

            const { data: updatedOrder, error: updateError } = await this.supabase
              .from('orders')
              .update(updateData)
              .eq('id', orderId)
              .select(`
                *,
                services (*)
              `)
              .single();

            if (!updateError) {
              return { success: true, order: updatedOrder };
            }
          }
        }
      }

      return { success: true, order };

    } catch (error) {
      console.error('Error checking order status:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  // Get available services
  async getAvailableServices(filters?: any): Promise<Service[]> {
    try {
      let query = this.supabase
        .from('services')
        .select('*')
        .eq('is_active', true);

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.brand) {
        query = query.contains('supported_brands', [filters.brand]);
      }

      if (filters?.search_term) {
        query = query.or(`name.ilike.%${filters.search_term}%,description.ilike.%${filters.search_term}%`);
      }

      const { data: services, error } = await query.order('name');

      if (error) {
        console.error('Error fetching services:', error);
        return [];
      }

      return services || [];

    } catch (error) {
      console.error('Error getting available services:', error);
      return [];
    }
  }

  // Validate service request
  async validateServiceRequest(serviceId: string, deviceInfo: any): Promise<ServiceValidation> {
    try {
      const { data: service, error } = await this.supabase
        .from('services')
        .select('*')
        .eq('id', serviceId)
        .eq('is_active', true)
        .single();

      if (error || !service) {
        return {
          isValid: false,
          errors: ['Service not found or inactive'],
          warnings: []
        };
      }

      const provider = this.providers.get(service.api_provider);
      if (!provider) {
        return {
          isValid: false,
          errors: ['Service provider not available'],
          warnings: []
        };
      }

      return await provider.validateService(service.api_service_id || serviceId, deviceInfo);

    } catch (error) {
      console.error('Error validating service request:', error);
      return {
        isValid: false,
        errors: ['Validation error'],
        warnings: []
      };
    }
  }

  // Get service statistics
  async getServiceStats(serviceId?: string): Promise<ServiceStats[]> {
    try {
      let query = this.supabase
        .from('orders')
        .select(`
          service_id,
          status,
          price_paid,
          created_at,
          completed_at,
          services (name)
        `);

      if (serviceId) {
        query = query.eq('service_id', serviceId);
      }

      const { data: orders, error } = await query;

      if (error) {
        console.error('Error fetching order stats:', error);
        return [];
      }

      // Group by service and calculate stats
      const statsMap = new Map<string, any>();

      for (const order of orders || []) {
        const serviceId = order.service_id;
        
        if (!statsMap.has(serviceId)) {
          statsMap.set(serviceId, {
            service_id: serviceId,
            service_name: order.services?.name || 'Unknown',
            total_orders: 0,
            completed_orders: 0,
            failed_orders: 0,
            revenue: 0,
            completion_times: []
          });
        }

        const stats = statsMap.get(serviceId);
        stats.total_orders++;
        stats.revenue += order.price_paid;

        if (order.status === 'completed') {
          stats.completed_orders++;
          
          if (order.created_at && order.completed_at) {
            const completionTime = new Date(order.completed_at).getTime() - new Date(order.created_at).getTime();
            stats.completion_times.push(completionTime);
          }
        } else if (order.status === 'failed') {
          stats.failed_orders++;
        }
      }

      // Calculate final stats
      const result: ServiceStats[] = [];
      for (const [serviceId, stats] of statsMap) {
        const avgCompletionTime = stats.completion_times.length > 0 
          ? stats.completion_times.reduce((a: number, b: number) => a + b, 0) / stats.completion_times.length
          : 0;

        result.push({
          total_orders: stats.total_orders,
          completed_orders: stats.completed_orders,
          failed_orders: stats.failed_orders,
          success_rate: stats.total_orders > 0 ? (stats.completed_orders / stats.total_orders) * 100 : 0,
          average_completion_time: Math.round(avgCompletionTime / (1000 * 60)), // Convert to minutes
          revenue: stats.revenue
        });
      }

      return result;

    } catch (error) {
      console.error('Error getting service stats:', error);
      return [];
    }
  }

  // Get provider health status
  async getProviderHealth(): Promise<ProviderStats[]> {
    const stats: ProviderStats[] = [];

    for (const [name, provider] of this.providers) {
      try {
        const isHealthy = await provider.healthCheck();
        
        stats.push({
          provider_name: name,
          total_requests: 0, // Would need to track this
          successful_requests: 0,
          failed_requests: 0,
          average_response_time: 0,
          uptime_percentage: isHealthy ? 100 : 0,
          last_request_at: new Date().toISOString()
        });
      } catch (error) {
        stats.push({
          provider_name: name,
          total_requests: 0,
          successful_requests: 0,
          failed_requests: 0,
          average_response_time: 0,
          uptime_percentage: 0
        });
      }
    }

    return stats;
  }

  // Refresh provider configurations
  async refreshProviders(): Promise<void> {
    this.providers.clear();
    await this.initializeProviders();
  }
}
