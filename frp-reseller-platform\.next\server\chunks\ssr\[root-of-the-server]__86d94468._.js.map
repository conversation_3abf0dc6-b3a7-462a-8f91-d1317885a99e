{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatCurrency(amount: number, currency = 'ARS'): string {\n  return new Intl.NumberFormat('es-AR', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('es-AR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date));\n}\n\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'hace unos segundos';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `hace ${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 30) {\n    return `hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`;\n  }\n\n  return formatDate(date);\n}\n\nexport function generateOrderId(): string {\n  const timestamp = Date.now().toString(36);\n  const randomStr = Math.random().toString(36).substring(2, 8);\n  return `ORD-${timestamp}-${randomStr}`.toUpperCase();\n}\n\nexport function validateIMEI(imei: string): boolean {\n  // Remove any non-digit characters\n  const cleanIMEI = imei.replace(/\\D/g, '');\n  \n  // IMEI should be 15 digits\n  if (cleanIMEI.length !== 15) {\n    return false;\n  }\n\n  // Luhn algorithm check\n  let sum = 0;\n  for (let i = 0; i < 14; i++) {\n    let digit = parseInt(cleanIMEI[i]);\n    if (i % 2 === 1) {\n      digit *= 2;\n      if (digit > 9) {\n        digit = Math.floor(digit / 10) + (digit % 10);\n      }\n    }\n    sum += digit;\n  }\n\n  const checkDigit = (10 - (sum % 10)) % 10;\n  return checkDigit === parseInt(cleanIMEI[14]);\n}\n\nexport function validateSerialNumber(serial: string): boolean {\n  // Basic validation for serial numbers\n  const cleanSerial = serial.trim();\n  return cleanSerial.length >= 8 && cleanSerial.length <= 20;\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors = {\n    pending: 'bg-yellow-100 text-yellow-800',\n    processing: 'bg-blue-100 text-blue-800',\n    completed: 'bg-green-100 text-green-800',\n    failed: 'bg-red-100 text-red-800',\n    cancelled: 'bg-gray-100 text-gray-800',\n  };\n\n  return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';\n}\n\nexport function getStatusText(status: string): string {\n  const statusTexts = {\n    pending: 'Pendiente',\n    processing: 'Procesando',\n    completed: 'Completado',\n    failed: 'Fallido',\n    cancelled: 'Cancelado',\n  };\n\n  return statusTexts[status as keyof typeof statusTexts] || status;\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,CAAC,KAAK,EAAE,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,IAAI;IACtE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,CAAC,KAAK,EAAE,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,IAAI;IAChE;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,IAAI;QACnB,OAAO,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,IAAI;IAC7D;IAEA,OAAO,WAAW;AACpB;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAC1D,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,WAAW,CAAC,WAAW;AACpD;AAEO,SAAS,aAAa,IAAY;IACvC,kCAAkC;IAClC,MAAM,YAAY,KAAK,OAAO,CAAC,OAAO;IAEtC,2BAA2B;IAC3B,IAAI,UAAU,MAAM,KAAK,IAAI;QAC3B,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,IAAI,QAAQ,SAAS,SAAS,CAAC,EAAE;QACjC,IAAI,IAAI,MAAM,GAAG;YACf,SAAS;YACT,IAAI,QAAQ,GAAG;gBACb,QAAQ,KAAK,KAAK,CAAC,QAAQ,MAAO,QAAQ;YAC5C;QACF;QACA,OAAO;IACT;IAEA,MAAM,aAAa,CAAC,KAAM,MAAM,EAAG,IAAI;IACvC,OAAO,eAAe,SAAS,SAAS,CAAC,GAAG;AAC9C;AAEO,SAAS,qBAAqB,MAAc;IACjD,sCAAsC;IACtC,MAAM,cAAc,OAAO,IAAI;IAC/B,OAAO,YAAY,MAAM,IAAI,KAAK,YAAY,MAAM,IAAI;AAC1D;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAe;QACnB,SAAS;QACT,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,WAAW;IACb;IAEA,OAAO,YAAY,CAAC,OAAoC,IAAI;AAC9D;AAEO,SAAS,cAAc,MAAc;IAC1C,MAAM,cAAc;QAClB,SAAS;QACT,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,WAAW;IACb;IAEA,OAAO,WAAW,CAAC,OAAmC,IAAI;AAC5D;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n}\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst Card: React.FC<CardProps> & {\n  Header: React.FC<CardHeaderProps>;\n  Content: React.FC<CardContentProps>;\n  Footer: React.FC<CardFooterProps>;\n} = ({ children, className, padding = 'md' }) => {\n  const paddingClasses = {\n    none: '',\n    sm: 'p-3',\n    md: 'p-4',\n    lg: 'p-6',\n  };\n\n  return (\n    <div className={cn('taringa-post shadow-sm', paddingClasses[padding], className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('border-b border-gray-200 pb-3 mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('border-t border-gray-200 pt-3 mt-4', className)}>\n      {children}\n    </div>\n  );\n};\n\nCard.Header = CardHeader;\nCard.Content = CardContent;\nCard.Footer = CardFooter;\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AACA;;;AAuBA,MAAM,OAIF,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,IAAI,EAAE;IAC1C,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,cAAc,CAAC,QAAQ,EAAE;kBACnE;;;;;;AAGP;AAEA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;AAEA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEA,KAAK,MAAM,GAAG;AACd,KAAK,OAAO,GAAG;AACf,KAAK,MAAM,GAAG;uCAEC", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'orange' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center font-bold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'taringa-button focus:ring-blue-500',\n      secondary: 'bg-gray-200 text-gray-900 border border-gray-300 hover:bg-gray-300 focus:ring-gray-500',\n      orange: 'taringa-button-orange focus:ring-orange-500',\n      outline: 'bg-transparent border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n      ghost: 'bg-transparent text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-xs rounded',\n      md: 'px-4 py-2 text-sm rounded-md',\n      lg: 'px-6 py-3 text-base rounded-lg',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface BadgeProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';\n  size?: 'sm' | 'md';\n  className?: string;\n}\n\nconst Badge: React.FC<BadgeProps> = ({ \n  children, \n  variant = 'default', \n  size = 'sm',\n  className \n}) => {\n  const baseClasses = 'status-badge inline-flex items-center justify-center';\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800',\n    success: 'bg-green-100 text-green-800',\n    warning: 'bg-yellow-100 text-yellow-800',\n    error: 'bg-red-100 text-red-800',\n    info: 'bg-blue-100 text-blue-800',\n  };\n\n  const sizes = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1.5 text-sm',\n  };\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,QAA8B,CAAC,EACnC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;kBAGD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Card from \"@/components/ui/Card\";\nimport Button from \"@/components/ui/Button\";\nimport Badge from \"@/components/ui/Badge\";\n\nexport default function Home() {\n  const featuredServices = [\n    {\n      id: 1,\n      name: \"Samsung FRP Bypass\",\n      description: \"Bypass FRP para dispositivos Samsung Galaxy. Soporte para modelos 2020-2025.\",\n      price: 15,\n      category: \"FRP\",\n      estimatedTime: \"5-15 min\",\n      popularity: \"Muy Popular\"\n    },\n    {\n      id: 2,\n      name: \"iPhone IMEI Repair\",\n      description: \"Reparación de IMEI para iPhone. Restaura la conectividad de red.\",\n      price: 25,\n      category: \"IMEI\",\n      estimatedTime: \"30-60 min\",\n      popularity: \"Popular\"\n    },\n    {\n      id: 3,\n      name: \"Xiaomi FRP Remove\",\n      description: \"Eliminación de FRP para dispositivos Xiaomi/Redmi/POCO.\",\n      price: 12,\n      category: \"FRP\",\n      estimatedTime: \"10-20 min\",\n      popularity: \"Trending\"\n    },\n    {\n      id: 4,\n      name: \"<PERSON><PERSON><PERSON> Unlock\",\n      description: \"Desbloqueo de bootloader y FRP para dispositivos Huawei.\",\n      price: 20,\n      category: \"Unlock\",\n      estimatedTime: \"15-30 min\",\n      popularity: \"Popular\"\n    }\n  ];\n\n  const recentOrders = [\n    { id: \"ORD-001\", device: \"Samsung Galaxy A52\", service: \"FRP Bypass\", status: \"completed\", time: \"hace 5 min\" },\n    { id: \"ORD-002\", device: \"iPhone 12 Pro\", service: \"IMEI Repair\", status: \"processing\", time: \"hace 15 min\" },\n    { id: \"ORD-003\", device: \"Xiaomi Redmi Note 10\", service: \"FRP Remove\", status: \"completed\", time: \"hace 30 min\" },\n    { id: \"ORD-004\", device: \"Huawei P30\", service: \"Unlock\", status: \"pending\", time: \"hace 1 hora\" },\n  ];\n\n  const getStatusVariant = (status: string) => {\n    switch (status) {\n      case 'completed': return 'success';\n      case 'processing': return 'warning';\n      case 'pending': return 'info';\n      case 'failed': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return 'Completado';\n      case 'processing': return 'Procesando';\n      case 'pending': return 'Pendiente';\n      case 'failed': return 'Fallido';\n      default: return status;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Hero Section */}\n      <Card className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n        <Card.Content>\n          <div className=\"py-8 text-center\">\n            <h1 className=\"text-4xl font-bold mb-4\">\n              Servicios FRP y Reparación IMEI\n            </h1>\n            <p className=\"text-xl mb-6 text-blue-100\">\n              Plataforma profesional para técnicos de celulares en Argentina\n            </p>\n            <div className=\"flex justify-center space-x-4\">\n              <Link href=\"/services\">\n                <Button variant=\"orange\" size=\"lg\">\n                  Ver Servicios\n                </Button>\n              </Link>\n              <Link href=\"/auth/register\">\n                <Button variant=\"outline\" size=\"lg\" className=\"text-white border-white hover:bg-white hover:text-blue-600\">\n                  Registrarse Gratis\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </Card.Content>\n      </Card>\n\n      {/* Featured Services */}\n      <div>\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"taringa-title text-2xl\">Servicios Destacados</h2>\n          <Link href=\"/services\">\n            <Button variant=\"outline\" size=\"sm\">\n              Ver Todos\n            </Button>\n          </Link>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {featuredServices.map((service) => (\n            <Card key={service.id} className=\"hover:shadow-md transition-shadow\">\n              <Card.Content>\n                <div className=\"flex justify-between items-start mb-3\">\n                  <div>\n                    <h3 className=\"taringa-title text-lg\">{service.name}</h3>\n                    <Badge variant=\"info\" size=\"sm\" className=\"mt-1\">\n                      {service.category}\n                    </Badge>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-2xl font-bold text-green-600\">${service.price}</p>\n                    <p className=\"taringa-meta\">{service.estimatedTime}</p>\n                  </div>\n                </div>\n                \n                <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                \n                <div className=\"flex justify-between items-center\">\n                  <Badge variant=\"warning\" size=\"sm\">\n                    {service.popularity}\n                  </Badge>\n                  <Link href={`/services/${service.id}`}>\n                    <Button size=\"sm\">\n                      Solicitar Servicio\n                    </Button>\n                  </Link>\n                </div>\n              </Card.Content>\n            </Card>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Orders */}\n      <Card>\n        <Card.Header>\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"taringa-title text-xl\">Órdenes Recientes</h2>\n            <Link href=\"/orders\">\n              <Button variant=\"ghost\" size=\"sm\">\n                Ver Todas\n              </Button>\n            </Link>\n          </div>\n        </Card.Header>\n        <Card.Content>\n          <div className=\"space-y-3\">\n            {recentOrders.map((order) => (\n              <div key={order.id} className=\"flex justify-between items-center p-3 bg-gray-50 rounded\">\n                <div>\n                  <p className=\"font-medium\">{order.device}</p>\n                  <p className=\"text-sm text-gray-600\">{order.service}</p>\n                  <p className=\"taringa-meta\">{order.time}</p>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"font-medium taringa-meta\">{order.id}</p>\n                  <Badge variant={getStatusVariant(order.status)} size=\"sm\">\n                    {getStatusText(order.status)}\n                  </Badge>\n                </div>\n              </div>\n            ))}\n          </div>\n        </Card.Content>\n      </Card>\n\n      {/* Call to Action */}\n      <Card className=\"bg-orange-50 border-orange-200\">\n        <Card.Content>\n          <div className=\"text-center py-6\">\n            <h3 className=\"text-xl font-bold text-orange-800 mb-2\">\n              ¿Eres revendedor?\n            </h3>\n            <p className=\"text-orange-700 mb-4\">\n              Únete a nuestro programa de revendedores y obtén precios especiales\n            </p>\n            <Link href=\"/reseller/apply\">\n              <Button variant=\"orange\">\n                Aplicar como Revendedor\n              </Button>\n            </Link>\n          </div>\n        </Card.Content>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;YACV,eAAe;YACf,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;YACV,eAAe;YACf,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;YACV,eAAe;YACf,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;YACV,eAAe;YACf,YAAY;QACd;KACD;IAED,MAAM,eAAe;QACnB;YAAE,IAAI;YAAW,QAAQ;YAAsB,SAAS;YAAc,QAAQ;YAAa,MAAM;QAAa;QAC9G;YAAE,IAAI;YAAW,QAAQ;YAAiB,SAAS;YAAe,QAAQ;YAAc,MAAM;QAAc;QAC5G;YAAE,IAAI;YAAW,QAAQ;YAAwB,SAAS;YAAc,QAAQ;YAAa,MAAM;QAAc;QACjH;YAAE,IAAI;YAAW,QAAQ;YAAc,SAAS;YAAU,QAAQ;YAAW,MAAM;QAAc;KAClG;IAED,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,UAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;8BACX,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAS,MAAK;sDAAK;;;;;;;;;;;kDAIrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrH,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;kCAMxC,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,gIAAA,CAAA,UAAI;gCAAkB,WAAU;0CAC/B,cAAA,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;;sDACX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB,QAAQ,IAAI;;;;;;sEACnD,8OAAC,iIAAA,CAAA,UAAK;4DAAC,SAAQ;4DAAO,MAAK;4DAAK,WAAU;sEACvC,QAAQ,QAAQ;;;;;;;;;;;;8DAGrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;gEAAoC;gEAAE,QAAQ,KAAK;;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAAgB,QAAQ,aAAa;;;;;;;;;;;;;;;;;;sDAItD,8OAAC;4CAAE,WAAU;sDAAsB,QAAQ,WAAW;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,UAAK;oDAAC,SAAQ;oDAAU,MAAK;8DAC3B,QAAQ,UAAU;;;;;;8DAErB,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;8DACnC,cAAA,8OAAC,kIAAA,CAAA,UAAM;wDAAC,MAAK;kEAAK;;;;;;;;;;;;;;;;;;;;;;;+BAtBf,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAkC3B,8OAAC,gIAAA,CAAA,UAAI;;kCACH,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;kCAMxC,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;kCACX,cAAA,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe,MAAM,MAAM;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAyB,MAAM,OAAO;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAAgB,MAAM,IAAI;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA4B,MAAM,EAAE;;;;;;8DACjD,8OAAC,iIAAA,CAAA,UAAK;oDAAC,SAAS,iBAAiB,MAAM,MAAM;oDAAG,MAAK;8DAClD,cAAc,MAAM,MAAM;;;;;;;;;;;;;mCATvB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BAmB1B,8OAAC,gIAAA,CAAA,UAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;8BACX,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;0CAGpC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC", "debugId": null}}]}