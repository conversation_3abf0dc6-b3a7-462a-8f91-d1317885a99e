{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatCurrency(amount: number, currency = 'ARS'): string {\n  return new Intl.NumberFormat('es-AR', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('es-AR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date));\n}\n\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'hace unos segundos';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `hace ${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 30) {\n    return `hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`;\n  }\n\n  return formatDate(date);\n}\n\nexport function generateOrderId(): string {\n  const timestamp = Date.now().toString(36);\n  const randomStr = Math.random().toString(36).substring(2, 8);\n  return `ORD-${timestamp}-${randomStr}`.toUpperCase();\n}\n\nexport function validateIMEI(imei: string): boolean {\n  // Remove any non-digit characters\n  const cleanIMEI = imei.replace(/\\D/g, '');\n  \n  // IMEI should be 15 digits\n  if (cleanIMEI.length !== 15) {\n    return false;\n  }\n\n  // Luhn algorithm check\n  let sum = 0;\n  for (let i = 0; i < 14; i++) {\n    let digit = parseInt(cleanIMEI[i]);\n    if (i % 2 === 1) {\n      digit *= 2;\n      if (digit > 9) {\n        digit = Math.floor(digit / 10) + (digit % 10);\n      }\n    }\n    sum += digit;\n  }\n\n  const checkDigit = (10 - (sum % 10)) % 10;\n  return checkDigit === parseInt(cleanIMEI[14]);\n}\n\nexport function validateSerialNumber(serial: string): boolean {\n  // Basic validation for serial numbers\n  const cleanSerial = serial.trim();\n  return cleanSerial.length >= 8 && cleanSerial.length <= 20;\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors = {\n    pending: 'bg-yellow-100 text-yellow-800',\n    processing: 'bg-blue-100 text-blue-800',\n    completed: 'bg-green-100 text-green-800',\n    failed: 'bg-red-100 text-red-800',\n    cancelled: 'bg-gray-100 text-gray-800',\n  };\n\n  return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';\n}\n\nexport function getStatusText(status: string): string {\n  const statusTexts = {\n    pending: 'Pendiente',\n    processing: 'Procesando',\n    completed: 'Completado',\n    failed: 'Fallido',\n    cancelled: 'Cancelado',\n  };\n\n  return statusTexts[status as keyof typeof statusTexts] || status;\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,CAAC,KAAK,EAAE,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,IAAI;IACtE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,CAAC,KAAK,EAAE,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,IAAI;IAChE;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,IAAI;QACnB,OAAO,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,IAAI;IAC7D;IAEA,OAAO,WAAW;AACpB;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAC1D,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,WAAW,CAAC,WAAW;AACpD;AAEO,SAAS,aAAa,IAAY;IACvC,kCAAkC;IAClC,MAAM,YAAY,KAAK,OAAO,CAAC,OAAO;IAEtC,2BAA2B;IAC3B,IAAI,UAAU,MAAM,KAAK,IAAI;QAC3B,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,IAAI,QAAQ,SAAS,SAAS,CAAC,EAAE;QACjC,IAAI,IAAI,MAAM,GAAG;YACf,SAAS;YACT,IAAI,QAAQ,GAAG;gBACb,QAAQ,KAAK,KAAK,CAAC,QAAQ,MAAO,QAAQ;YAC5C;QACF;QACA,OAAO;IACT;IAEA,MAAM,aAAa,CAAC,KAAM,MAAM,EAAG,IAAI;IACvC,OAAO,eAAe,SAAS,SAAS,CAAC,GAAG;AAC9C;AAEO,SAAS,qBAAqB,MAAc;IACjD,sCAAsC;IACtC,MAAM,cAAc,OAAO,IAAI;IAC/B,OAAO,YAAY,MAAM,IAAI,KAAK,YAAY,MAAM,IAAI;AAC1D;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAe;QACnB,SAAS;QACT,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,WAAW;IACb;IAEA,OAAO,YAAY,CAAC,OAAoC,IAAI;AAC9D;AAEO,SAAS,cAAc,MAAc;IAC1C,MAAM,cAAc;QAClB,SAAS;QACT,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,WAAW;IACb;IAEA,OAAO,WAAW,CAAC,OAAmC,IAAI;AAC5D;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'orange' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center font-bold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'taringa-button focus:ring-blue-500',\n      secondary: 'bg-gray-200 text-gray-900 border border-gray-300 hover:bg-gray-300 focus:ring-gray-500',\n      orange: 'taringa-button-orange focus:ring-orange-500',\n      outline: 'bg-transparent border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n      ghost: 'bg-transparent text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-xs rounded',\n      md: 'px-4 py-2 text-sm rounded-md',\n      lg: 'px-6 py-3 text-base rounded-lg',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\ninterface HeaderProps {\n  user?: {\n    name: string;\n    email: string;\n    role: string;\n    credits: number;\n  } | null;\n}\n\nconst Header: React.FC<HeaderProps> = ({ user }) => {\n  const pathname = usePathname();\n\n  const navigation = [\n    { name: 'Inicio', href: '/', current: pathname === '/' },\n    { name: 'Servicios', href: '/services', current: pathname === '/services' },\n    { name: '<PERSON><PERSON>', href: '/orders', current: pathname === '/orders' },\n    { name: 'Precios', href: '/pricing', current: pathname === '/pricing' },\n  ];\n\n  const adminNavigation = [\n    { name: 'Admin', href: '/admin', current: pathname.startsWith('/admin') },\n  ];\n\n  const resellerNavigation = [\n    { name: 'Panel Revendedor', href: '/reseller', current: pathname.startsWith('/reseller') },\n  ];\n\n  return (\n    <header className=\"taringa-header\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center\">\n                <span className=\"text-blue-600 font-bold text-lg\">F</span>\n              </div>\n              <span className=\"text-white font-bold text-xl\">FRP Reseller</span>\n            </Link>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex space-x-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                  item.current\n                    ? 'bg-blue-700 text-white'\n                    : 'text-blue-100 hover:bg-blue-700 hover:text-white'\n                )}\n              >\n                {item.name}\n              </Link>\n            ))}\n            \n            {user?.role === 'admin' && adminNavigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                  item.current\n                    ? 'bg-orange-600 text-white'\n                    : 'text-orange-200 hover:bg-orange-600 hover:text-white'\n                )}\n              >\n                {item.name}\n              </Link>\n            ))}\n\n            {user?.role === 'reseller' && resellerNavigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                  item.current\n                    ? 'bg-orange-600 text-white'\n                    : 'text-orange-200 hover:bg-orange-600 hover:text-white'\n                )}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* User menu */}\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <>\n                <div className=\"text-white text-sm\">\n                  <span className=\"font-medium\">{user.name}</span>\n                  <div className=\"text-blue-200 text-xs\">\n                    Créditos: ${user.credits}\n                  </div>\n                </div>\n                <Button\n                  variant=\"orange\"\n                  size=\"sm\"\n                  onClick={() => {/* TODO: Implement logout */}}\n                >\n                  Salir\n                </Button>\n              </>\n            ) : (\n              <div className=\"flex space-x-2\">\n                <Link href=\"/auth/login\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"text-white border-white hover:bg-white hover:text-blue-600\">\n                    Iniciar Sesión\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button variant=\"orange\" size=\"sm\">\n                    Registrarse\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      <div className=\"md:hidden\">\n        <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-800\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'block px-3 py-2 rounded-md text-base font-medium transition-colors',\n                item.current\n                  ? 'bg-blue-700 text-white'\n                  : 'text-blue-100 hover:bg-blue-700 hover:text-white'\n              )}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAiBA,MAAM,SAAgC,CAAC,EAAE,IAAI,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAU,MAAM;YAAK,SAAS,aAAa;QAAI;QACvD;YAAE,MAAM;YAAa,MAAM;YAAa,SAAS,aAAa;QAAY;QAC1E;YAAE,MAAM;YAAe,MAAM;YAAW,SAAS,aAAa;QAAU;QACxE;YAAE,MAAM;YAAW,MAAM;YAAY,SAAS,aAAa;QAAW;KACvE;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAS,MAAM;YAAU,SAAS,SAAS,UAAU,CAAC;QAAU;KACzE;IAED,MAAM,qBAAqB;QACzB;YAAE,MAAM;YAAoB,MAAM;YAAa,SAAS,SAAS,UAAU,CAAC;QAAa;KAC1F;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;kDAEpD,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,KAAK,OAAO,GACR,2BACA;kDAGL,KAAK,IAAI;uCATL,KAAK,IAAI;;;;;gCAajB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,KAAK,OAAO,GACR,6BACA;kDAGL,KAAK,IAAI;uCATL,KAAK,IAAI;;;;;gCAajB,MAAM,SAAS,cAAc,mBAAmB,GAAG,CAAC,CAAC,qBACpD,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,KAAK,OAAO,GACR,6BACA;kDAGL,KAAK,IAAI;uCATL,KAAK,IAAI;;;;;;;;;;;sCAepB,8OAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACxC,8OAAC;gDAAI,WAAU;;oDAAwB;oDACzB,KAAK,OAAO;;;;;;;;;;;;;kDAG5B,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,KAAmC;kDAC7C;;;;;;;6DAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAA6D;;;;;;;;;;;kDAI7G,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAS,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,KAAK,OAAO,GACR,2BACA;sCAGL,KAAK,IAAI;2BATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAgB5B;uCAEe", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n}\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst Card: React.FC<CardProps> & {\n  Header: React.FC<CardHeaderProps>;\n  Content: React.FC<CardContentProps>;\n  Footer: React.FC<CardFooterProps>;\n} = ({ children, className, padding = 'md' }) => {\n  const paddingClasses = {\n    none: '',\n    sm: 'p-3',\n    md: 'p-4',\n    lg: 'p-6',\n  };\n\n  return (\n    <div className={cn('taringa-post shadow-sm', paddingClasses[padding], className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('border-b border-gray-200 pb-3 mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('border-t border-gray-200 pt-3 mt-4', className)}>\n      {children}\n    </div>\n  );\n};\n\nCard.Header = CardHeader;\nCard.Content = CardContent;\nCard.Footer = CardFooter;\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AACA;;;AAuBA,MAAM,OAIF,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,IAAI,EAAE;IAC1C,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,cAAc,CAAC,QAAQ,EAAE;kBACnE;;;;;;AAGP;AAEA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;AAEA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;AAEA,KAAK,MAAM,GAAG;AACd,KAAK,OAAO,GAAG;AACf,KAAK,MAAM,GAAG;uCAEC", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface BadgeProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';\n  size?: 'sm' | 'md';\n  className?: string;\n}\n\nconst Badge: React.FC<BadgeProps> = ({ \n  children, \n  variant = 'default', \n  size = 'sm',\n  className \n}) => {\n  const baseClasses = 'status-badge inline-flex items-center justify-center';\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800',\n    success: 'bg-green-100 text-green-800',\n    warning: 'bg-yellow-100 text-yellow-800',\n    error: 'bg-red-100 text-red-800',\n    info: 'bg-blue-100 text-blue-800',\n  };\n\n  const sizes = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1.5 text-sm',\n  };\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,QAA8B,CAAC,EACnC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;kBAGD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/RubickFRP/frp-reseller-platform/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Card from '@/components/ui/Card';\nimport Badge from '@/components/ui/Badge';\nimport Button from '@/components/ui/Button';\n\ninterface SidebarProps {\n  user?: {\n    name: string;\n    email: string;\n    role: string;\n    credits: number;\n  } | null;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ user }) => {\n  const quickStats = [\n    { label: 'Órdenes Hoy', value: '12', color: 'info' as const },\n    { label: 'Completadas', value: '8', color: 'success' as const },\n    { label: 'Pendientes', value: '4', color: 'warning' as const },\n  ];\n\n  const popularServices = [\n    { name: 'Samsung FRP Bypass', price: '$15', category: 'FRP' },\n    { name: 'iPhone IMEI Repair', price: '$25', category: 'IMEI' },\n    { name: 'Xiaomi FRP Remove', price: '$12', category: 'FRP' },\n    { name: '<PERSON>aw<PERSON> Unlock', price: '$20', category: 'Unlock' },\n  ];\n\n  const recentActivity = [\n    { action: 'Orden completada', device: 'Samsung A52', time: 'hace 2 min' },\n    { action: 'Nueva orden', device: 'iPhone 12', time: 'hace 15 min' },\n    { action: 'Pago recibido', device: 'Créditos +$50', time: 'hace 1 hora' },\n  ];\n\n  return (\n    <aside className=\"w-80 space-y-4\">\n      {/* User Info Card */}\n      {user && (\n        <Card>\n          <Card.Header>\n            <h3 className=\"taringa-title text-lg\">Mi Cuenta</h3>\n          </Card.Header>\n          <Card.Content>\n            <div className=\"space-y-3\">\n              <div>\n                <p className=\"font-medium\">{user.name}</p>\n                <p className=\"taringa-meta\">{user.email}</p>\n                <Badge variant=\"info\" className=\"mt-1\">\n                  {user.role === 'admin' ? 'Administrador' : \n                   user.role === 'reseller' ? 'Revendedor' : 'Cliente'}\n                </Badge>\n              </div>\n              <div className=\"bg-blue-50 p-3 rounded\">\n                <p className=\"text-sm font-medium text-blue-900\">\n                  Créditos Disponibles\n                </p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  ${user.credits}\n                </p>\n              </div>\n              <Link href=\"/credits/purchase\">\n                <Button variant=\"orange\" size=\"sm\" className=\"w-full\">\n                  Comprar Créditos\n                </Button>\n              </Link>\n            </div>\n          </Card.Content>\n        </Card>\n      )}\n\n      {/* Quick Stats */}\n      <Card>\n        <Card.Header>\n          <h3 className=\"taringa-title text-lg\">Estadísticas Rápidas</h3>\n        </Card.Header>\n        <Card.Content>\n          <div className=\"space-y-3\">\n            {quickStats.map((stat, index) => (\n              <div key={index} className=\"flex justify-between items-center\">\n                <span className=\"text-sm text-gray-600\">{stat.label}</span>\n                <Badge variant={stat.color}>{stat.value}</Badge>\n              </div>\n            ))}\n          </div>\n        </Card.Content>\n      </Card>\n\n      {/* Popular Services */}\n      <Card>\n        <Card.Header>\n          <h3 className=\"taringa-title text-lg\">Servicios Populares</h3>\n        </Card.Header>\n        <Card.Content>\n          <div className=\"space-y-3\">\n            {popularServices.map((service, index) => (\n              <div key={index} className=\"border-b border-gray-100 pb-2 last:border-b-0\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <p className=\"text-sm font-medium\">{service.name}</p>\n                    <p className=\"taringa-meta\">{service.category}</p>\n                  </div>\n                  <span className=\"text-sm font-bold text-green-600\">\n                    {service.price}\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </Card.Content>\n        <Card.Footer>\n          <Link href=\"/services\">\n            <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n              Ver Todos los Servicios\n            </Button>\n          </Link>\n        </Card.Footer>\n      </Card>\n\n      {/* Recent Activity */}\n      <Card>\n        <Card.Header>\n          <h3 className=\"taringa-title text-lg\">Actividad Reciente</h3>\n        </Card.Header>\n        <Card.Content>\n          <div className=\"space-y-3\">\n            {recentActivity.map((activity, index) => (\n              <div key={index} className=\"border-b border-gray-100 pb-2 last:border-b-0\">\n                <p className=\"text-sm font-medium\">{activity.action}</p>\n                <p className=\"text-sm text-gray-600\">{activity.device}</p>\n                <p className=\"taringa-meta\">{activity.time}</p>\n              </div>\n            ))}\n          </div>\n        </Card.Content>\n        <Card.Footer>\n          <Link href=\"/activity\">\n            <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">\n              Ver Toda la Actividad\n            </Button>\n          </Link>\n        </Card.Footer>\n      </Card>\n\n      {/* Support */}\n      <Card>\n        <Card.Header>\n          <h3 className=\"taringa-title text-lg\">Soporte</h3>\n        </Card.Header>\n        <Card.Content>\n          <div className=\"space-y-2\">\n            <Link href=\"/help\" className=\"block text-sm text-blue-600 hover:underline\">\n              📚 Centro de Ayuda\n            </Link>\n            <Link href=\"/contact\" className=\"block text-sm text-blue-600 hover:underline\">\n              💬 Contactar Soporte\n            </Link>\n            <Link href=\"/telegram\" className=\"block text-sm text-blue-600 hover:underline\">\n              📱 Grupo Telegram\n            </Link>\n            <Link href=\"/whatsapp\" className=\"block text-sm text-blue-600 hover:underline\">\n              📞 WhatsApp\n            </Link>\n          </div>\n        </Card.Content>\n      </Card>\n    </aside>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAiBA,MAAM,UAAkC,CAAC,EAAE,IAAI,EAAE;IAC/C,MAAM,aAAa;QACjB;YAAE,OAAO;YAAe,OAAO;YAAM,OAAO;QAAgB;QAC5D;YAAE,OAAO;YAAe,OAAO;YAAK,OAAO;QAAmB;QAC9D;YAAE,OAAO;YAAc,OAAO;YAAK,OAAO;QAAmB;KAC9D;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAsB,OAAO;YAAO,UAAU;QAAM;QAC5D;YAAE,MAAM;YAAsB,OAAO;YAAO,UAAU;QAAO;QAC7D;YAAE,MAAM;YAAqB,OAAO;YAAO,UAAU;QAAM;QAC3D;YAAE,MAAM;YAAiB,OAAO;YAAO,UAAU;QAAS;KAC3D;IAED,MAAM,iBAAiB;QACrB;YAAE,QAAQ;YAAoB,QAAQ;YAAe,MAAM;QAAa;QACxE;YAAE,QAAQ;YAAe,QAAQ;YAAa,MAAM;QAAc;QAClE;YAAE,QAAQ;YAAiB,QAAQ;YAAiB,MAAM;QAAc;KACzE;IAED,qBACE,8OAAC;QAAM,WAAU;;YAEd,sBACC,8OAAC,gIAAA,CAAA,UAAI;;kCACH,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;kCAExC,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAe,KAAK,IAAI;;;;;;sDACrC,8OAAC;4CAAE,WAAU;sDAAgB,KAAK,KAAK;;;;;;sDACvC,8OAAC,iIAAA,CAAA,UAAK;4CAAC,SAAQ;4CAAO,WAAU;sDAC7B,KAAK,IAAI,KAAK,UAAU,kBACxB,KAAK,IAAI,KAAK,aAAa,eAAe;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDAGjD,8OAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,KAAK,OAAO;;;;;;;;;;;;;8CAGlB,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAS,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhE,8OAAC,gIAAA,CAAA,UAAI;;kCACH,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;kCAExC,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;kCACX,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAK,WAAU;sDAAyB,KAAK,KAAK;;;;;;sDACnD,8OAAC,iIAAA,CAAA,UAAK;4CAAC,SAAS,KAAK,KAAK;sDAAG,KAAK,KAAK;;;;;;;mCAF/B;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC,gIAAA,CAAA,UAAI;;kCACH,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;kCAExC,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;kCACX,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAuB,QAAQ,IAAI;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEAAgB,QAAQ,QAAQ;;;;;;;;;;;;0DAE/C,8OAAC;gDAAK,WAAU;0DACb,QAAQ,KAAK;;;;;;;;;;;;mCAPV;;;;;;;;;;;;;;;kCAchB,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAS;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,8OAAC,gIAAA,CAAA,UAAI;;kCACH,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;kCAExC,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;kCACX,cAAA,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAE,WAAU;sDAAuB,SAAS,MAAM;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAyB,SAAS,MAAM;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB,SAAS,IAAI;;;;;;;mCAHlC;;;;;;;;;;;;;;;kCAQhB,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAAS;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,8OAAC,gIAAA,CAAA,UAAI;;kCACH,8OAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;kCACV,cAAA,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;kCAExC,8OAAC,gIAAA,CAAA,UAAI,CAAC,OAAO;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAA8C;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8C;;;;;;8CAG9E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAA8C;;;;;;8CAG/E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3F;uCAEe", "debugId": null}}]}