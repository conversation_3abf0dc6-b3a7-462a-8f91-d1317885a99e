{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "sourcesContent": ["import type { NormalModule, webpack } from 'next/dist/compiled/webpack/webpack'\nimport { RSC_MOD_REF_PROXY_ALIAS } from '../../../../lib/constants'\nimport {\n  BARREL_OPTIMIZATION_PREFIX,\n  RSC_MODULE_TYPES,\n} from '../../../../shared/lib/constants'\nimport { warnOnce } from '../../../../shared/lib/utils/warn-once'\nimport { getRSCModuleInformation } from '../../../analysis/get-page-static-info'\nimport { formatBarrelOptimizedResource } from '../../utils'\nimport { getModuleBuildInfo } from '../get-module-build-info'\nimport type {\n  javascript,\n  LoaderContext,\n} from 'next/dist/compiled/webpack/webpack'\n\ntype SourceType = javascript.JavascriptParser['sourceType'] | 'commonjs'\n\nconst noopHeadPath = require.resolve('next/dist/client/components/noop-head')\n// For edge runtime it will be aliased to esm version by webpack\nconst MODULE_PROXY_PATH =\n  'next/dist/build/webpack/loaders/next-flight-loader/module-proxy'\n\nexport function getAssumedSourceType(\n  mod: webpack.Module,\n  sourceType: SourceType\n): SourceType {\n  const buildInfo = getModuleBuildInfo(mod)\n  const detectedClientEntryType = buildInfo?.rsc?.clientEntryType\n  const clientRefs = buildInfo?.rsc?.clientRefs || []\n\n  // It's tricky to detect the type of a client boundary, but we should always\n  // use the `module` type when we can, to support `export *` and `export from`\n  // syntax in other modules that import this client boundary.\n\n  if (sourceType === 'auto') {\n    if (detectedClientEntryType === 'auto') {\n      if (clientRefs.length === 0) {\n        // If there's zero export detected in the client boundary, and it's the\n        // `auto` type, we can safely assume it's a CJS module because it doesn't\n        // have ESM exports.\n        return 'commonjs'\n      } else if (!clientRefs.includes('*')) {\n        // Otherwise, we assume it's an ESM module.\n        return 'module'\n      }\n    } else if (detectedClientEntryType === 'cjs') {\n      return 'commonjs'\n    }\n  }\n\n  return sourceType\n}\n\nexport default function transformSource(\n  this: LoaderContext<undefined>,\n  source: string,\n  sourceMap: any\n) {\n  // Avoid buffer to be consumed\n  if (typeof source !== 'string') {\n    throw new Error('Expected source to have been transformed to a string.')\n  }\n  const module = this._module!\n\n  // Assign the RSC meta information to buildInfo.\n  // Exclude next internal files which are not marked as client files\n  const buildInfo = getModuleBuildInfo(module)\n  buildInfo.rsc = getRSCModuleInformation(source, true)\n  let prefix = ''\n  if (process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN) {\n    const rscModuleInformationJson = JSON.stringify(buildInfo.rsc)\n    prefix = `/* __rspack_internal_rsc_module_information_do_not_use__ ${rscModuleInformationJson} */\\n`\n    source = prefix + source\n  }\n\n  // Resource key is the unique identifier for the resource. When RSC renders\n  // a client module, that key is used to identify that module across all compiler\n  // layers.\n  //\n  // Usually it's the module's file path + the export name (e.g. `foo.js#bar`).\n  // But with Barrel Optimizations, one file can be splitted into multiple modules,\n  // so when you import `foo.js#bar` and `foo.js#baz`, they are actually different\n  // \"foo.js\" being created by the Barrel Loader (one only exports `bar`, the other\n  // only exports `baz`).\n  //\n  // Because of that, we must add another query param to the resource key to\n  // differentiate them.\n  let resourceKey: string = this.resourcePath\n  if (module.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n    resourceKey = formatBarrelOptimizedResource(\n      resourceKey,\n      module.matchResource\n    )\n  }\n\n  // A client boundary.\n  if (buildInfo.rsc?.type === RSC_MODULE_TYPES.client) {\n    const assumedSourceType = getAssumedSourceType(\n      module,\n      sourceTypeFromModule(module)\n    )\n\n    const clientRefs = buildInfo.rsc.clientRefs!\n    const stringifiedResourceKey = JSON.stringify(resourceKey)\n\n    if (assumedSourceType === 'module') {\n      if (clientRefs.length === 0) {\n        return this.callback(null, 'export {}')\n      }\n\n      if (clientRefs.includes('*')) {\n        this.callback(\n          new Error(\n            `It's currently unsupported to use \"export *\" in a client boundary. Please use named exports instead.`\n          )\n        )\n        return\n      }\n\n      let esmSource =\n        prefix +\n        `\\\nimport { registerClientReference } from \"react-server-dom-webpack/server\";\n`\n      for (const ref of clientRefs) {\n        if (ref === 'default') {\n          esmSource += `export default registerClientReference(\nfunction() { throw new Error(${JSON.stringify(`Attempted to call the default \\\nexport of ${stringifiedResourceKey} from the server, but it's on the client. \\\nIt's not possible to invoke a client function from the server, it can only be \\\nrendered as a Component or passed to props of a Client Component.`)}); },\n${stringifiedResourceKey},\n\"default\",\n);\\n`\n        } else {\n          esmSource += `export const ${ref} = registerClientReference(\nfunction() { throw new Error(${JSON.stringify(`Attempted to call ${ref}() from \\\nthe server but ${ref} is on the client. It's not possible to invoke a client \\\nfunction from the server, it can only be rendered as a Component or passed to \\\nprops of a Client Component.`)}); },\n${stringifiedResourceKey},\n${JSON.stringify(ref)},\n);`\n        }\n      }\n\n      return this.callback(null, esmSource, sourceMap)\n    } else if (assumedSourceType === 'commonjs') {\n      let cjsSource =\n        prefix +\n        `\\\nconst { createProxy } = require(\"${MODULE_PROXY_PATH}\")\n\nmodule.exports = createProxy(${stringifiedResourceKey})\n`\n      return this.callback(null, cjsSource, sourceMap)\n    }\n  }\n\n  if (buildInfo.rsc?.type !== RSC_MODULE_TYPES.client) {\n    if (noopHeadPath === this.resourcePath) {\n      warnOnce(\n        `Warning: You're using \\`next/head\\` inside the \\`app\\` directory, please migrate to the Metadata API. See https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration#step-3-migrating-nexthead for more details.`\n      )\n    }\n  }\n\n  const replacedSource = source.replace(\n    RSC_MOD_REF_PROXY_ALIAS,\n    MODULE_PROXY_PATH\n  )\n  this.callback(null, replacedSource, sourceMap)\n}\n\nfunction sourceTypeFromModule(module: NormalModule): SourceType {\n  const moduleType = module.type\n  switch (moduleType) {\n    case 'javascript/auto':\n      return 'auto'\n    case 'javascript/dynamic':\n      return 'script'\n    case 'javascript/esm':\n      return 'module'\n    default:\n      throw new Error('Unexpected module type ' + moduleType)\n  }\n}\n"], "names": ["RSC_MOD_REF_PROXY_ALIAS", "BARREL_OPTIMIZATION_PREFIX", "RSC_MODULE_TYPES", "warnOnce", "getRSCModuleInformation", "formatBarrelOptimizedResource", "getModuleBuildInfo", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "getAssumedSourceType", "mod", "sourceType", "buildInfo", "detectedClientEntryType", "rsc", "clientEntryType", "clientRefs", "length", "includes", "transformSource", "source", "sourceMap", "module", "Error", "_module", "prefix", "process", "env", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "rscModuleInformationJson", "JSON", "stringify", "resourceKey", "resourcePath", "matchResource", "startsWith", "type", "client", "assumedSourceType", "sourceTypeFromModule", "stringifiedResourceKey", "callback", "esmSource", "ref", "cjsSource", "replacedSource", "replace", "moduleType"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,4BAA2B;AACnE,SACEC,0BAA0B,EAC1BC,gBAAgB,QACX,mCAAkC;AACzC,SAASC,QAAQ,QAAQ,yCAAwC;AACjE,SAASC,uBAAuB,QAAQ,yCAAwC;AAChF,SAASC,6BAA6B,QAAQ,cAAa;AAC3D,SAASC,kBAAkB,QAAQ,2BAA0B;AAQ7D,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEF,OAAO,SAASC,qBACdC,GAAmB,EACnBC,UAAsB;QAGUC,gBACbA;IAFnB,MAAMA,YAAYR,mBAAmBM;IACrC,MAAMG,0BAA0BD,8BAAAA,iBAAAA,UAAWE,GAAG,qBAAdF,eAAgBG,eAAe;IAC/D,MAAMC,aAAaJ,CAAAA,8BAAAA,kBAAAA,UAAWE,GAAG,qBAAdF,gBAAgBI,UAAU,KAAI,EAAE;IAEnD,4EAA4E;IAC5E,6EAA6E;IAC7E,4DAA4D;IAE5D,IAAIL,eAAe,QAAQ;QACzB,IAAIE,4BAA4B,QAAQ;YACtC,IAAIG,WAAWC,MAAM,KAAK,GAAG;gBAC3B,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpB,OAAO;YACT,OAAO,IAAI,CAACD,WAAWE,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3C,OAAO;YACT;QACF,OAAO,IAAIL,4BAA4B,OAAO;YAC5C,OAAO;QACT;IACF;IAEA,OAAOF;AACT;AAEA,eAAe,SAASQ,gBAEtBC,MAAc,EACdC,SAAc;QAgCVC,uBAQAV,gBA+DAA;IArGJ,8BAA8B;IAC9B,IAAI,OAAOQ,WAAW,UAAU;QAC9B,MAAM,qBAAkE,CAAlE,IAAIG,MAAM,0DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiE;IACzE;IACA,MAAMD,SAAS,IAAI,CAACE,OAAO;IAE3B,gDAAgD;IAChD,mEAAmE;IACnE,MAAMZ,YAAYR,mBAAmBkB;IACrCV,UAAUE,GAAG,GAAGZ,wBAAwBkB,QAAQ;IAChD,IAAIK,SAAS;IACb,IAAIC,QAAQC,GAAG,CAACC,kCAAkC,EAAE;QAClD,MAAMC,2BAA2BC,KAAKC,SAAS,CAACnB,UAAUE,GAAG;QAC7DW,SAAS,CAAC,yDAAyD,EAAEI,yBAAyB,KAAK,CAAC;QACpGT,SAASK,SAASL;IACpB;IAEA,2EAA2E;IAC3E,gFAAgF;IAChF,UAAU;IACV,EAAE;IACF,6EAA6E;IAC7E,iFAAiF;IACjF,gFAAgF;IAChF,iFAAiF;IACjF,uBAAuB;IACvB,EAAE;IACF,0EAA0E;IAC1E,sBAAsB;IACtB,IAAIY,cAAsB,IAAI,CAACC,YAAY;IAC3C,KAAIX,wBAAAA,OAAOY,aAAa,qBAApBZ,sBAAsBa,UAAU,CAACpC,6BAA6B;QAChEiC,cAAc7B,8BACZ6B,aACAV,OAAOY,aAAa;IAExB;IAEA,qBAAqB;IACrB,IAAItB,EAAAA,iBAAAA,UAAUE,GAAG,qBAAbF,eAAewB,IAAI,MAAKpC,iBAAiBqC,MAAM,EAAE;QACnD,MAAMC,oBAAoB7B,qBACxBa,QACAiB,qBAAqBjB;QAGvB,MAAMN,aAAaJ,UAAUE,GAAG,CAACE,UAAU;QAC3C,MAAMwB,yBAAyBV,KAAKC,SAAS,CAACC;QAE9C,IAAIM,sBAAsB,UAAU;YAClC,IAAItB,WAAWC,MAAM,KAAK,GAAG;gBAC3B,OAAO,IAAI,CAACwB,QAAQ,CAAC,MAAM;YAC7B;YAEA,IAAIzB,WAAWE,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACuB,QAAQ,CACX,qBAEC,CAFD,IAAIlB,MACF,CAAC,oGAAoG,CAAC,GADxG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEA;gBAEF;YACF;YAEA,IAAImB,YACFjB,SACA,CAAC;;AAET,CAAC;YACK,KAAK,MAAMkB,OAAO3B,WAAY;gBAC5B,IAAI2B,QAAQ,WAAW;oBACrBD,aAAa,CAAC;6BACK,EAAEZ,KAAKC,SAAS,CAAC,CAAC;UACrC,EAAES,uBAAuB;;iEAE8B,CAAC,EAAE;AACpE,EAAEA,uBAAuB;;IAErB,CAAC;gBACG,OAAO;oBACLE,aAAa,CAAC,aAAa,EAAEC,IAAI;6BACd,EAAEb,KAAKC,SAAS,CAAC,CAAC,kBAAkB,EAAEY,IAAI;eACxD,EAAEA,IAAI;;4BAEO,CAAC,EAAE;AAC/B,EAAEH,uBAAuB;AACzB,EAAEV,KAAKC,SAAS,CAACY,KAAK;EACpB,CAAC;gBACK;YACF;YAEA,OAAO,IAAI,CAACF,QAAQ,CAAC,MAAMC,WAAWrB;QACxC,OAAO,IAAIiB,sBAAsB,YAAY;YAC3C,IAAIM,YACFnB,SACA,CAAC;iCACwB,EAAEjB,kBAAkB;;6BAExB,EAAEgC,uBAAuB;AACtD,CAAC;YACK,OAAO,IAAI,CAACC,QAAQ,CAAC,MAAMG,WAAWvB;QACxC;IACF;IAEA,IAAIT,EAAAA,kBAAAA,UAAUE,GAAG,qBAAbF,gBAAewB,IAAI,MAAKpC,iBAAiBqC,MAAM,EAAE;QACnD,IAAIhC,iBAAiB,IAAI,CAAC4B,YAAY,EAAE;YACtChC,SACE,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,MAAM4C,iBAAiBzB,OAAO0B,OAAO,CACnChD,yBACAU;IAEF,IAAI,CAACiC,QAAQ,CAAC,MAAMI,gBAAgBxB;AACtC;AAEA,SAASkB,qBAAqBjB,MAAoB;IAChD,MAAMyB,aAAazB,OAAOc,IAAI;IAC9B,OAAQW;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,MAAM,qBAAiD,CAAjD,IAAIxB,MAAM,4BAA4BwB,aAAtC,qBAAA;uBAAA;4BAAA;8BAAA;YAAgD;IAC1D;AACF", "ignoreList": [0]}