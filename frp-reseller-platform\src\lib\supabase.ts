import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Admin client for server-side operations
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Database schema types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          role: 'client' | 'reseller' | 'admin';
          credits: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name: string;
          role?: 'client' | 'reseller' | 'admin';
          credits?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          role?: 'client' | 'reseller' | 'admin';
          credits?: number;
          is_active?: boolean;
          updated_at?: string;
        };
      };
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          phone: string | null;
          country: string;
          city: string | null;
          avatar: string | null;
          whatsapp: string | null;
          telegram: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          phone?: string | null;
          country: string;
          city?: string | null;
          avatar?: string | null;
          whatsapp?: string | null;
          telegram?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          phone?: string | null;
          country?: string;
          city?: string | null;
          avatar?: string | null;
          whatsapp?: string | null;
          telegram?: string | null;
          updated_at?: string;
        };
      };
      services: {
        Row: {
          id: string;
          name: string;
          description: string;
          category: 'frp' | 'imei' | 'unlock' | 'repair';
          provider: 'legitunlocks' | 'custom';
          price: number;
          reseller_price: number;
          is_active: boolean;
          estimated_time: string;
          requirements: string[];
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description: string;
          category: 'frp' | 'imei' | 'unlock' | 'repair';
          provider: 'legitunlocks' | 'custom';
          price: number;
          reseller_price: number;
          is_active?: boolean;
          estimated_time: string;
          requirements: string[];
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          name?: string;
          description?: string;
          category?: 'frp' | 'imei' | 'unlock' | 'repair';
          provider?: 'legitunlocks' | 'custom';
          price?: number;
          reseller_price?: number;
          is_active?: boolean;
          estimated_time?: string;
          requirements?: string[];
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          user_id: string;
          service_id: string;
          status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
          device_info: any;
          result: any | null;
          price: number;
          created_at: string;
          updated_at: string;
          completed_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          service_id: string;
          status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
          device_info: any;
          result?: any | null;
          price: number;
          created_at?: string;
          updated_at?: string;
          completed_at?: string | null;
        };
        Update: {
          status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
          device_info?: any;
          result?: any | null;
          price?: number;
          updated_at?: string;
          completed_at?: string | null;
        };
      };
      transactions: {
        Row: {
          id: string;
          user_id: string;
          type: 'credit_purchase' | 'service_payment' | 'commission' | 'refund';
          amount: number;
          status: 'pending' | 'completed' | 'failed' | 'cancelled';
          payment_method: string;
          external_id: string | null;
          description: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          type: 'credit_purchase' | 'service_payment' | 'commission' | 'refund';
          amount: number;
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          payment_method: string;
          external_id?: string | null;
          description: string;
          created_at?: string;
        };
        Update: {
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          external_id?: string | null;
          description?: string;
        };
      };
    };
  };
}
