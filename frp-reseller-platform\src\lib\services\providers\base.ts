// Base interface for API service providers

import { 
  ApiServiceRequest, 
  ApiServiceResponse, 
  ApiStatusResponse, 
  DeviceInfo, 
  OrderStatus,
  ServiceValidation,
  DeviceValidation 
} from '@/types/services';

export abstract class BaseApiProvider {
  protected name: string;
  protected baseUrl: string;
  protected apiKey: string;
  protected isActive: boolean;
  protected configuration: Record<string, any>;

  constructor(
    name: string,
    baseUrl: string,
    apiKey: string,
    configuration: Record<string, any> = {}
  ) {
    this.name = name;
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.isActive = true;
    this.configuration = configuration;
  }

  // Abstract methods that must be implemented by each provider
  abstract submitOrder(request: ApiServiceRequest): Promise<ApiServiceResponse>;
  abstract checkStatus(orderId: string): Promise<ApiStatusResponse>;
  abstract validateService(serviceId: string, deviceInfo: DeviceInfo): Promise<ServiceValidation>;
  abstract validateDevice(deviceInfo: DeviceInfo): Promise<DeviceValidation>;
  abstract getAvailableServices(): Promise<any[]>;

  // Common utility methods
  protected async makeRequest(
    endpoint: string, 
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any,
    headers?: Record<string, string>
  ): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
      ...headers
    };

    const config: RequestInit = {
      method,
      headers: defaultHeaders,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error(`API request failed for ${this.name}:`, error);
      throw error;
    }
  }

  protected mapOrderStatus(providerStatus: string): OrderStatus {
    // Default mapping - should be overridden by specific providers
    const statusMap: Record<string, OrderStatus> = {
      'pending': 'pending',
      'processing': 'processing',
      'in_progress': 'processing',
      'completed': 'completed',
      'success': 'completed',
      'failed': 'failed',
      'error': 'failed',
      'cancelled': 'cancelled',
      'refunded': 'refunded'
    };

    return statusMap[providerStatus.toLowerCase()] || 'pending';
  }

  protected validateRequiredFields(deviceInfo: DeviceInfo, requiredFields: string[]): string[] {
    const missingFields: string[] = [];
    
    for (const field of requiredFields) {
      if (!deviceInfo[field as keyof DeviceInfo]) {
        missingFields.push(field);
      }
    }

    return missingFields;
  }

  protected isValidImei(imei: string): boolean {
    // Basic IMEI validation (15 digits)
    const imeiRegex = /^\d{15}$/;
    return imeiRegex.test(imei);
  }

  protected isValidSerialNumber(serial: string): boolean {
    // Basic serial number validation (alphanumeric, 6-20 characters)
    const serialRegex = /^[A-Za-z0-9]{6,20}$/;
    return serialRegex.test(serial);
  }

  // Getter methods
  getName(): string {
    return this.name;
  }

  getBaseUrl(): string {
    return this.baseUrl;
  }

  getIsActive(): boolean {
    return this.isActive;
  }

  getConfiguration(): Record<string, any> {
    return this.configuration;
  }

  // Setter methods
  setIsActive(active: boolean): void {
    this.isActive = active;
  }

  setConfiguration(config: Record<string, any>): void {
    this.configuration = { ...this.configuration, ...config };
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      // Default health check - can be overridden
      await this.makeRequest('/health', 'GET');
      return true;
    } catch (error) {
      console.error(`Health check failed for ${this.name}:`, error);
      return false;
    }
  }

  // Rate limiting helper
  protected async rateLimitDelay(): Promise<void> {
    const delay = this.configuration.rateLimitDelay || 1000; // 1 second default
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  // Retry logic helper
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          break;
        }

        console.warn(`Attempt ${attempt} failed for ${this.name}, retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }

    throw lastError!;
  }

  // Webhook validation helper
  protected validateWebhookSignature(
    payload: string,
    signature: string,
    secret: string
  ): boolean {
    // Default implementation - should be overridden by specific providers
    return true;
  }

  // Error handling helper
  protected handleApiError(error: any): ApiServiceResponse {
    return {
      success: false,
      status: 'failed',
      message: error.message || 'Unknown API error',
      error: error.toString()
    };
  }

  // Logging helper
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${this.name}] [${level.toUpperCase()}] ${message}`;
    
    if (data) {
      console[level](logMessage, data);
    } else {
      console[level](logMessage);
    }
  }
}
