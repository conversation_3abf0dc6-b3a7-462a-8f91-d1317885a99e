import Link from "next/link";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";

export default function Home() {
  const featuredServices = [
    {
      id: 1,
      name: "Samsung FRP Bypass",
      description: "Bypass FRP para dispositivos Samsung Galaxy. Soporte para modelos 2020-2025.",
      price: 15,
      category: "FRP",
      estimatedTime: "5-15 min",
      popularity: "Muy Popular"
    },
    {
      id: 2,
      name: "iPhone IMEI Repair",
      description: "Reparación de IMEI para iPhone. Restaura la conectividad de red.",
      price: 25,
      category: "IMEI",
      estimatedTime: "30-60 min",
      popularity: "Popular"
    },
    {
      id: 3,
      name: "Xiaomi FRP Remove",
      description: "Eliminación de FRP para dispositivos Xiaomi/Redmi/POCO.",
      price: 12,
      category: "FRP",
      estimatedTime: "10-20 min",
      popularity: "Trending"
    },
    {
      id: 4,
      name: "<PERSON><PERSON><PERSON> Unlock",
      description: "Desbloqueo de bootloader y FRP para dispositivos Huawei.",
      price: 20,
      category: "Unlock",
      estimatedTime: "15-30 min",
      popularity: "Popular"
    }
  ];

  const recentOrders = [
    { id: "ORD-001", device: "Samsung Galaxy A52", service: "FRP Bypass", status: "completed", time: "hace 5 min" },
    { id: "ORD-002", device: "iPhone 12 Pro", service: "IMEI Repair", status: "processing", time: "hace 15 min" },
    { id: "ORD-003", device: "Xiaomi Redmi Note 10", service: "FRP Remove", status: "completed", time: "hace 30 min" },
    { id: "ORD-004", device: "Huawei P30", service: "Unlock", status: "pending", time: "hace 1 hora" },
  ];

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'pending': return 'info';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completado';
      case 'processing': return 'Procesando';
      case 'pending': return 'Pendiente';
      case 'failed': return 'Fallido';
      default: return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <Card className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <Card.Content>
          <div className="py-8 text-center">
            <h1 className="text-4xl font-bold mb-4">
              Servicios FRP y Reparación IMEI
            </h1>
            <p className="text-xl mb-6 text-blue-100">
              Plataforma profesional para técnicos de celulares en Argentina
            </p>
            <div className="flex justify-center space-x-4">
              <Link href="/services">
                <Button variant="orange" size="lg">
                  Ver Servicios
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-blue-600">
                  Registrarse Gratis
                </Button>
              </Link>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Featured Services */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="taringa-title text-2xl">Servicios Destacados</h2>
          <Link href="/services">
            <Button variant="outline" size="sm">
              Ver Todos
            </Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {featuredServices.map((service) => (
            <Card key={service.id} className="hover:shadow-md transition-shadow">
              <Card.Content>
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="taringa-title text-lg">{service.name}</h3>
                    <Badge variant="info" size="sm" className="mt-1">
                      {service.category}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-green-600">${service.price}</p>
                    <p className="taringa-meta">{service.estimatedTime}</p>
                  </div>
                </div>
                
                <p className="text-gray-600 mb-4">{service.description}</p>
                
                <div className="flex justify-between items-center">
                  <Badge variant="warning" size="sm">
                    {service.popularity}
                  </Badge>
                  <Link href={`/services/${service.id}`}>
                    <Button size="sm">
                      Solicitar Servicio
                    </Button>
                  </Link>
                </div>
              </Card.Content>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Orders */}
      <Card>
        <Card.Header>
          <div className="flex justify-between items-center">
            <h2 className="taringa-title text-xl">Órdenes Recientes</h2>
            <Link href="/orders">
              <Button variant="ghost" size="sm">
                Ver Todas
              </Button>
            </Link>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-3">
            {recentOrders.map((order) => (
              <div key={order.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <div>
                  <p className="font-medium">{order.device}</p>
                  <p className="text-sm text-gray-600">{order.service}</p>
                  <p className="taringa-meta">{order.time}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium taringa-meta">{order.id}</p>
                  <Badge variant={getStatusVariant(order.status)} size="sm">
                    {getStatusText(order.status)}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Call to Action */}
      <Card className="bg-orange-50 border-orange-200">
        <Card.Content>
          <div className="text-center py-6">
            <h3 className="text-xl font-bold text-orange-800 mb-2">
              ¿Eres revendedor?
            </h3>
            <p className="text-orange-700 mb-4">
              Únete a nuestro programa de revendedores y obtén precios especiales
            </p>
            <Link href="/reseller/apply">
              <Button variant="orange">
                Aplicar como Revendedor
              </Button>
            </Link>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}
