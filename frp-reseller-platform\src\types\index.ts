// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  credits: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  profile?: UserProfile;
}

export type UserRole = 'client' | 'reseller' | 'admin';

export interface UserProfile {
  phone?: string;
  country: string;
  city?: string;
  avatar?: string;
  whatsapp?: string;
  telegram?: string;
}

// Service Types
export interface Service {
  id: string;
  name: string;
  description: string;
  category: ServiceCategory;
  provider: ServiceProvider;
  price: number;
  resellerPrice: number;
  isActive: boolean;
  estimatedTime: string;
  requirements: string[];
  createdAt: string;
  updatedAt: string;
}

export type ServiceCategory = 'frp' | 'imei' | 'unlock' | 'repair';
export type ServiceProvider = 'legitunlocks' | 'custom';

// Order Types
export interface Order {
  id: string;
  userId: string;
  serviceId: string;
  status: OrderStatus;
  deviceInfo: DeviceInfo;
  result?: OrderResult;
  price: number;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export type OrderStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';

export interface DeviceInfo {
  imei?: string;
  serialNumber?: string;
  model: string;
  brand: string;
  additionalInfo?: Record<string, any>;
}

export interface OrderResult {
  success: boolean;
  data?: any;
  message?: string;
  files?: string[];
}

// Payment Types
export interface Transaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  paymentMethod: string;
  externalId?: string;
  description: string;
  createdAt: string;
}

export type TransactionType = 'credit_purchase' | 'service_payment' | 'commission' | 'refund';
export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}
