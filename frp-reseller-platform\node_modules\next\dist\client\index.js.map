{"version": 3, "sources": ["../../src/client/index.tsx"], "sourcesContent": ["/* global location */\n// imports polyfill from `@next/polyfill-module` after build.\nimport '../build/polyfills/polyfill-module'\nimport type Router from '../shared/lib/router/router'\nimport type {\n  AppComponent,\n  AppProps,\n  PrivateRouteInfo,\n} from '../shared/lib/router/router'\n\nimport React, { type JSX } from 'react'\nimport ReactDOM from 'react-dom/client'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport mitt from '../shared/lib/mitt'\nimport type { MittEmitter } from '../shared/lib/mitt'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { disableSmoothScrollDuringRouteTransition } from '../shared/lib/router/utils/disable-smooth-scroll'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  urlQueryToSearchParams,\n  assign,\n} from '../shared/lib/router/utils/querystring'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getURL, loadGetInitialProps, ST } from '../shared/lib/utils'\nimport type { NextWebVitalsMetric, NEXT_DATA } from '../shared/lib/utils'\nimport { Portal } from './portal'\nimport initHeadManager from './head-manager'\nimport PageLoader from './page-loader'\nimport type { StyleSheetTuple } from './page-loader'\nimport { RouteAnnouncer } from './route-announcer'\nimport { createRouter, makePublicRouterInstance } from './router'\nimport { getProperError } from '../lib/is-error'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { removeBasePath } from './remove-base-path'\nimport { hasBasePath } from './has-base-path'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { onRecoverableError } from './react-client-callbacks/on-recoverable-error'\nimport tracer from './tracing/tracer'\nimport { isNextRouterError } from './components/is-next-router-error'\n\n/// <reference types=\"react-dom/experimental\" />\n\ndeclare global {\n  interface Window {\n    /* test fns */\n    __NEXT_HYDRATED?: boolean\n    __NEXT_HYDRATED_AT?: number\n    __NEXT_HYDRATED_CB?: () => void\n\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n    __NEXT_P: any[]\n  }\n}\ntype RenderRouteInfo = PrivateRouteInfo & {\n  App: AppComponent\n  scroll?: { x: number; y: number } | null\n  isHydratePass?: boolean\n}\ntype RenderErrorProps = Omit<RenderRouteInfo, 'Component' | 'styleSheets'>\ntype RegisterFn = (input: [string, () => void]) => void\n\nexport const version = process.env.__NEXT_VERSION\nexport let router: Router\nexport const emitter: MittEmitter<string> = mitt()\n\nconst looseToArray = <T extends {}>(input: any): T[] => [].slice.call(input)\n\nlet initialData: NEXT_DATA\nlet defaultLocale: string | undefined = undefined\nlet asPath: string\nlet pageLoader: PageLoader\nlet appElement: HTMLElement | null\nlet headManager: {\n  mountedInstances: Set<unknown>\n  updateHead: (head: JSX.Element[]) => void\n  getIsSsr?: () => boolean\n}\nlet initialMatchesMiddleware = false\nlet lastAppProps: AppProps\n\nlet lastRenderReject: (() => void) | null\nlet devClient: any\n\nlet CachedApp: AppComponent, onPerfEntry: (metric: any) => void\nlet CachedComponent: React.ComponentType\n\nclass Container extends React.Component<{\n  children?: React.ReactNode\n  fn: (err: Error, info?: any) => void\n}> {\n  componentDidCatch(componentErr: Error, info: any) {\n    this.props.fn(componentErr, info)\n  }\n\n  componentDidMount() {\n    this.scrollToHash()\n\n    // We need to replace the router state if:\n    // - the page was (auto) exported and has a query string or search (hash)\n    // - it was auto exported and is a dynamic route (to provide params)\n    // - if it is a client-side skeleton (fallback render)\n    // - if middleware matches the current page (may have rewrite params)\n    // - if rewrites in next.config.js match (may have rewrite params)\n    if (\n      router.isSsr &&\n      (initialData.isFallback ||\n        (initialData.nextExport &&\n          (isDynamicRoute(router.pathname) ||\n            location.search ||\n            process.env.__NEXT_HAS_REWRITES ||\n            initialMatchesMiddleware)) ||\n        (initialData.props &&\n          initialData.props.__N_SSG &&\n          (location.search ||\n            process.env.__NEXT_HAS_REWRITES ||\n            initialMatchesMiddleware)))\n    ) {\n      // update query on mount for exported pages\n      router\n        .replace(\n          router.pathname +\n            '?' +\n            String(\n              assign(\n                urlQueryToSearchParams(router.query),\n                new URLSearchParams(location.search)\n              )\n            ),\n          asPath,\n          {\n            // @ts-ignore\n            // WARNING: `_h` is an internal option for handing Next.js\n            // client-side hydration. Your app should _never_ use this property.\n            // It may change at any time without notice.\n            _h: 1,\n            // Fallback pages must trigger the data fetch, so the transition is\n            // not shallow.\n            // Other pages (strictly updating query) happens shallowly, as data\n            // requirements would already be present.\n            shallow: !initialData.isFallback && !initialMatchesMiddleware,\n          }\n        )\n        .catch((err) => {\n          if (!err.cancelled) throw err\n        })\n    }\n  }\n\n  componentDidUpdate() {\n    this.scrollToHash()\n  }\n\n  scrollToHash() {\n    let { hash } = location\n    hash = hash && hash.substring(1)\n    if (!hash) return\n\n    const el: HTMLElement | null = document.getElementById(hash)\n    if (!el) return\n\n    // If we call scrollIntoView() in here without a setTimeout\n    // it won't scroll properly.\n    setTimeout(() => el.scrollIntoView(), 0)\n  }\n\n  render() {\n    if (process.env.NODE_ENV === 'production') {\n      return this.props.children\n    } else {\n      const { PagesDevOverlayBridge } =\n        require('../next-devtools/userspace/pages/pages-dev-overlay-setup') as typeof import('../next-devtools/userspace/pages/pages-dev-overlay-setup')\n      return (\n        <PagesDevOverlayBridge>{this.props.children}</PagesDevOverlayBridge>\n      )\n    }\n  }\n}\n\nexport async function initialize(opts: { devClient?: any } = {}): Promise<{\n  assetPrefix: string\n}> {\n  // This makes sure this specific lines are removed in production\n  if (process.env.NODE_ENV === 'development') {\n    tracer.onSpanEnd(\n      (\n        require('./tracing/report-to-socket') as typeof import('./tracing/report-to-socket')\n      ).default\n    )\n    devClient = opts.devClient\n  }\n\n  initialData = JSON.parse(\n    document.getElementById('__NEXT_DATA__')!.textContent!\n  )\n  window.__NEXT_DATA__ = initialData\n\n  defaultLocale = initialData.defaultLocale\n  const prefix: string = initialData.assetPrefix || ''\n  // With dynamic assetPrefix it's no longer possible to set assetPrefix at the build time\n  // So, this is how we do it in the client side at runtime\n  ;(self as any).__next_set_public_path__(`${prefix}/_next/`) //eslint-disable-line\n\n  // Initialize next/config with the environment configuration\n  setConfig({\n    serverRuntimeConfig: {},\n    publicRuntimeConfig: initialData.runtimeConfig || {},\n  })\n\n  asPath = getURL()\n\n  // make sure not to attempt stripping basePath for 404s\n  if (hasBasePath(asPath)) {\n    asPath = removeBasePath(asPath)\n  }\n\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { normalizeLocalePath } =\n      require('../shared/lib/i18n/normalize-locale-path') as typeof import('../shared/lib/i18n/normalize-locale-path')\n\n    const { detectDomainLocale } =\n      require('../shared/lib/i18n/detect-domain-locale') as typeof import('../shared/lib/i18n/detect-domain-locale')\n\n    const { parseRelativeUrl } =\n      require('../shared/lib/router/utils/parse-relative-url') as typeof import('../shared/lib/router/utils/parse-relative-url')\n\n    const { formatUrl } =\n      require('../shared/lib/router/utils/format-url') as typeof import('../shared/lib/router/utils/format-url')\n\n    if (initialData.locales) {\n      const parsedAs = parseRelativeUrl(asPath)\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        initialData.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        parsedAs.pathname = localePathResult.pathname\n        asPath = formatUrl(parsedAs)\n      } else {\n        // derive the default locale if it wasn't detected in the asPath\n        // since we don't prerender static pages with all possible default\n        // locales\n        defaultLocale = initialData.locale\n      }\n\n      // attempt detecting default locale based on hostname\n      const detectedDomain = detectDomainLocale(\n        process.env.__NEXT_I18N_DOMAINS as any,\n        window.location.hostname\n      )\n\n      // TODO: investigate if defaultLocale needs to be populated after\n      // hydration to prevent mismatched renders\n      if (detectedDomain) {\n        defaultLocale = detectedDomain.defaultLocale\n      }\n    }\n  }\n\n  if (initialData.scriptLoader) {\n    const { initScriptLoader } =\n      require('./script') as typeof import('./script')\n    initScriptLoader(initialData.scriptLoader)\n  }\n\n  pageLoader = new PageLoader(initialData.buildId, prefix)\n\n  const register: RegisterFn = ([r, f]) =>\n    pageLoader.routeLoader.onEntrypoint(r, f)\n  if (window.__NEXT_P) {\n    // Defer page registration for another tick. This will increase the overall\n    // latency in hydrating the page, but reduce the total blocking time.\n    window.__NEXT_P.map((p) => setTimeout(() => register(p), 0))\n  }\n  window.__NEXT_P = []\n  ;(window.__NEXT_P as any).push = register\n\n  headManager = initHeadManager()\n  headManager.getIsSsr = () => {\n    return router.isSsr\n  }\n\n  appElement = document.getElementById('__next')\n  return { assetPrefix: prefix }\n}\n\nfunction renderApp(App: AppComponent, appProps: AppProps) {\n  return <App {...appProps} />\n}\n\nfunction AppContainer({\n  children,\n}: React.PropsWithChildren<{}>): React.ReactElement {\n  // Create a memoized value for next/navigation router context.\n  const adaptedForAppRouter = React.useMemo(() => {\n    return adaptForAppRouterInstance(router)\n  }, [])\n  return (\n    <Container\n      fn={(error) =>\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        renderError({ App: CachedApp, err: error }).catch((err) =>\n          console.error('Error rendering page: ', err)\n        )\n      }\n    >\n      <AppRouterContext.Provider value={adaptedForAppRouter}>\n        <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n          <PathnameContextProviderAdapter\n            router={router}\n            isAutoExport={self.__NEXT_DATA__.autoExport ?? false}\n          >\n            <PathParamsContext.Provider value={adaptForPathParams(router)}>\n              <RouterContext.Provider value={makePublicRouterInstance(router)}>\n                <HeadManagerContext.Provider value={headManager}>\n                  <ImageConfigContext.Provider\n                    value={\n                      process.env\n                        .__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n                    }\n                  >\n                    {children}\n                  </ImageConfigContext.Provider>\n                </HeadManagerContext.Provider>\n              </RouterContext.Provider>\n            </PathParamsContext.Provider>\n          </PathnameContextProviderAdapter>\n        </SearchParamsContext.Provider>\n      </AppRouterContext.Provider>\n    </Container>\n  )\n}\n\nconst wrapApp =\n  (App: AppComponent) =>\n  (wrappedAppProps: Record<string, any>): JSX.Element => {\n    const appProps: AppProps = {\n      ...wrappedAppProps,\n      Component: CachedComponent,\n      err: initialData.err,\n      router,\n    }\n    return <AppContainer>{renderApp(App, appProps)}</AppContainer>\n  }\n\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps: RenderErrorProps): Promise<any> {\n  let { App, err } = renderErrorProps\n\n  // In development runtime errors are caught by our overlay\n  // In production we catch runtime errors using componentDidCatch which will trigger renderError\n  if (process.env.NODE_ENV !== 'production') {\n    // A Next.js rendering runtime error is always unrecoverable\n    // FIXME: let's make this recoverable (error in GIP client-transition)\n    devClient.onUnrecoverableError()\n\n    // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n    // render itself.\n    // TODO: Fix disabled eslint rule\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return doRender({\n      App: () => null,\n      props: {},\n      Component: () => null,\n      styleSheets: [],\n    })\n  }\n\n  // Make sure we log the error to the console, otherwise users can't track down issues.\n  console.error(err)\n  console.error(\n    `A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred`\n  )\n\n  return pageLoader\n    .loadPage('/_error')\n    .then(({ page: ErrorComponent, styleSheets }) => {\n      return lastAppProps?.Component === ErrorComponent\n        ? import('../pages/_error')\n            .then((errorModule) => {\n              return import('../pages/_app').then((appModule) => {\n                App = appModule.default as any as AppComponent\n                renderErrorProps.App = App\n                return errorModule\n              })\n            })\n            .then((m) => ({\n              ErrorComponent: m.default as React.ComponentType<{}>,\n              styleSheets: [],\n            }))\n        : { ErrorComponent, styleSheets }\n    })\n    .then(({ ErrorComponent, styleSheets }) => {\n      // In production we do a normal render with the `ErrorComponent` as component.\n      // If we've gotten here upon initial render, we can use the props from the server.\n      // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n      const AppTree = wrapApp(App)\n      const appCtx = {\n        Component: ErrorComponent,\n        AppTree,\n        router,\n        ctx: {\n          err,\n          pathname: initialData.page,\n          query: initialData.query,\n          asPath,\n          AppTree,\n        },\n      }\n      return Promise.resolve(\n        renderErrorProps.props?.err\n          ? renderErrorProps.props\n          : loadGetInitialProps(App, appCtx)\n      ).then((initProps) =>\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        doRender({\n          ...renderErrorProps,\n          err,\n          Component: ErrorComponent,\n          styleSheets,\n          props: initProps,\n        })\n      )\n    })\n}\n\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head({ callback }: { callback: () => void }): null {\n  // We use `useLayoutEffect` to guarantee the callback is executed\n  // as soon as React flushes the update.\n  React.useLayoutEffect(() => callback(), [callback])\n  return null\n}\n\nconst performanceMarks = {\n  navigationStart: 'navigationStart',\n  beforeRender: 'beforeRender',\n  afterRender: 'afterRender',\n  afterHydrate: 'afterHydrate',\n  routeChange: 'routeChange',\n} as const\n\nconst performanceMeasures = {\n  hydration: 'Next.js-hydration',\n  beforeHydration: 'Next.js-before-hydration',\n  routeChangeToRender: 'Next.js-route-change-to-render',\n  render: 'Next.js-render',\n} as const\n\nlet reactRoot: any = null\n// On initial render a hydrate should always happen\nlet shouldHydrate: boolean = true\n\nfunction clearMarks(): void {\n  ;[\n    performanceMarks.beforeRender,\n    performanceMarks.afterHydrate,\n    performanceMarks.afterRender,\n    performanceMarks.routeChange,\n  ].forEach((mark) => performance.clearMarks(mark))\n}\n\nfunction markHydrateComplete(): void {\n  if (!ST) return\n\n  performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n\n  const hasBeforeRenderMark = performance.getEntriesByName(\n    performanceMarks.beforeRender,\n    'mark'\n  ).length\n  if (hasBeforeRenderMark) {\n    const beforeHydrationMeasure = performance.measure(\n      performanceMeasures.beforeHydration,\n      performanceMarks.navigationStart,\n      performanceMarks.beforeRender\n    )\n\n    const hydrationMeasure = performance.measure(\n      performanceMeasures.hydration,\n      performanceMarks.beforeRender,\n      performanceMarks.afterHydrate\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n      beforeHydrationMeasure &&\n      hydrationMeasure\n    ) {\n      tracer\n        .startSpan('navigation-to-hydration', {\n          startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n          attributes: {\n            pathname: location.pathname,\n            query: location.search,\n          },\n        })\n        .end(\n          performance.timeOrigin +\n            hydrationMeasure.startTime +\n            hydrationMeasure.duration\n        )\n    }\n  }\n\n  if (onPerfEntry) {\n    performance\n      .getEntriesByName(performanceMeasures.hydration)\n      .forEach(onPerfEntry)\n  }\n  clearMarks()\n}\n\nfunction markRenderComplete(): void {\n  if (!ST) return\n\n  performance.mark(performanceMarks.afterRender) // mark end of render\n  const navStartEntries: PerformanceEntryList = performance.getEntriesByName(\n    performanceMarks.routeChange,\n    'mark'\n  )\n\n  if (!navStartEntries.length) return\n\n  const hasBeforeRenderMark = performance.getEntriesByName(\n    performanceMarks.beforeRender,\n    'mark'\n  ).length\n\n  if (hasBeforeRenderMark) {\n    performance.measure(\n      performanceMeasures.routeChangeToRender,\n      navStartEntries[0].name,\n      performanceMarks.beforeRender\n    )\n    performance.measure(\n      performanceMeasures.render,\n      performanceMarks.beforeRender,\n      performanceMarks.afterRender\n    )\n    if (onPerfEntry) {\n      performance\n        .getEntriesByName(performanceMeasures.render)\n        .forEach(onPerfEntry)\n      performance\n        .getEntriesByName(performanceMeasures.routeChangeToRender)\n        .forEach(onPerfEntry)\n    }\n  }\n\n  clearMarks()\n  ;[\n    performanceMeasures.routeChangeToRender,\n    performanceMeasures.render,\n  ].forEach((measure) => performance.clearMeasures(measure))\n}\n\nfunction renderReactElement(\n  domEl: HTMLElement,\n  fn: (cb: () => void) => JSX.Element\n): void {\n  // mark start of hydrate/render\n  if (ST) {\n    performance.mark(performanceMarks.beforeRender)\n  }\n\n  const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete)\n  if (!reactRoot) {\n    // Unlike with createRoot, you don't need a separate root.render() call here\n    reactRoot = ReactDOM.hydrateRoot(domEl, reactEl, {\n      onRecoverableError,\n    })\n    // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n    shouldHydrate = false\n  } else {\n    const startTransition = (React as any).startTransition\n    startTransition(() => {\n      reactRoot.render(reactEl)\n    })\n  }\n}\n\nfunction Root({\n  callbacks,\n  children,\n}: React.PropsWithChildren<{\n  callbacks: Array<() => void>\n}>): React.ReactElement {\n  // We use `useLayoutEffect` to guarantee the callbacks are executed\n  // as soon as React flushes the update\n  React.useLayoutEffect(\n    () => callbacks.forEach((callback) => callback()),\n    [callbacks]\n  )\n\n  if (process.env.__NEXT_TEST_MODE) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      window.__NEXT_HYDRATED = true\n      window.__NEXT_HYDRATED_AT = performance.now()\n\n      if (window.__NEXT_HYDRATED_CB) {\n        window.__NEXT_HYDRATED_CB()\n      }\n    }, [])\n  }\n\n  return children as React.ReactElement\n}\n\nfunction doRender(input: RenderRouteInfo): Promise<any> {\n  let { App, Component, props, err }: RenderRouteInfo = input\n  let styleSheets: StyleSheetTuple[] | undefined =\n    'initial' in input ? undefined : input.styleSheets\n  Component = Component || lastAppProps.Component\n  props = props || lastAppProps.props\n\n  const appProps: AppProps = {\n    ...props,\n    Component,\n    err,\n    router,\n  }\n  // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n  lastAppProps = appProps\n\n  let canceled: boolean = false\n  let resolvePromise: () => void\n  const renderPromise = new Promise<void>((resolve, reject) => {\n    if (lastRenderReject) {\n      lastRenderReject()\n    }\n    resolvePromise = () => {\n      lastRenderReject = null\n      resolve()\n    }\n    lastRenderReject = () => {\n      canceled = true\n      lastRenderReject = null\n\n      const error: any = new Error('Cancel rendering route')\n      error.cancelled = true\n      reject(error)\n    }\n  })\n\n  // This function has a return type to ensure it doesn't start returning a\n  // Promise. It should remain synchronous.\n  function onStart(): boolean {\n    if (\n      !styleSheets ||\n      // We use `style-loader` in development, so we don't need to do anything\n      // unless we're in production:\n      process.env.NODE_ENV !== 'production'\n    ) {\n      return false\n    }\n\n    const currentStyleTags: HTMLStyleElement[] = looseToArray<HTMLStyleElement>(\n      document.querySelectorAll('style[data-n-href]')\n    )\n    const currentHrefs: Set<string | null> = new Set(\n      currentStyleTags.map((tag) => tag.getAttribute('data-n-href'))\n    )\n\n    const noscript: Element | null = document.querySelector(\n      'noscript[data-n-css]'\n    )\n    const nonce: string | null | undefined =\n      noscript?.getAttribute('data-n-css')\n\n    styleSheets.forEach(({ href, text }: { href: string; text: any }) => {\n      if (!currentHrefs.has(href)) {\n        const styleTag = document.createElement('style')\n        styleTag.setAttribute('data-n-href', href)\n        styleTag.setAttribute('media', 'x')\n\n        if (nonce) {\n          styleTag.setAttribute('nonce', nonce)\n        }\n\n        document.head.appendChild(styleTag)\n        styleTag.appendChild(document.createTextNode(text))\n      }\n    })\n    return true\n  }\n\n  function onHeadCommit(): void {\n    if (\n      // Turbopack has it's own css injection handling, this code ends up removing the CSS.\n      !process.env.TURBOPACK &&\n      // We use `style-loader` in development, so we don't need to do anything\n      // unless we're in production:\n      process.env.NODE_ENV === 'production' &&\n      // We can skip this during hydration. Running it wont cause any harm, but\n      // we may as well save the CPU cycles:\n      styleSheets &&\n      // Ensure this render was not canceled\n      !canceled\n    ) {\n      const desiredHrefs: Set<string> = new Set(styleSheets.map((s) => s.href))\n      const currentStyleTags: HTMLStyleElement[] =\n        looseToArray<HTMLStyleElement>(\n          document.querySelectorAll('style[data-n-href]')\n        )\n      const currentHrefs: string[] = currentStyleTags.map(\n        (tag) => tag.getAttribute('data-n-href')!\n      )\n\n      // Toggle `<style>` tags on or off depending on if they're needed:\n      for (let idx = 0; idx < currentHrefs.length; ++idx) {\n        if (desiredHrefs.has(currentHrefs[idx])) {\n          currentStyleTags[idx].removeAttribute('media')\n        } else {\n          currentStyleTags[idx].setAttribute('media', 'x')\n        }\n      }\n\n      // Reorder styles into intended order:\n      let referenceNode: Element | null = document.querySelector(\n        'noscript[data-n-css]'\n      )\n      if (\n        // This should be an invariant:\n        referenceNode\n      ) {\n        styleSheets.forEach(({ href }: { href: string }) => {\n          const targetTag: Element | null = document.querySelector(\n            `style[data-n-href=\"${href}\"]`\n          )\n          if (\n            // This should be an invariant:\n            targetTag\n          ) {\n            referenceNode!.parentNode!.insertBefore(\n              targetTag,\n              referenceNode!.nextSibling\n            )\n            referenceNode = targetTag\n          }\n        })\n      }\n\n      // Finally, clean up server rendered stylesheets:\n      looseToArray<HTMLLinkElement>(\n        document.querySelectorAll('link[data-n-p]')\n      ).forEach((el) => {\n        el.parentNode!.removeChild(el)\n      })\n    }\n\n    if (input.scroll) {\n      const { x, y } = input.scroll\n      disableSmoothScrollDuringRouteTransition(() => {\n        window.scrollTo(x, y)\n      })\n    }\n  }\n\n  function onRootCommit(): void {\n    resolvePromise()\n  }\n\n  onStart()\n\n  const elem: JSX.Element = (\n    <>\n      <Head callback={onHeadCommit} />\n      <AppContainer>\n        {renderApp(App, appProps)}\n        <Portal type=\"next-route-announcer\">\n          <RouteAnnouncer />\n        </Portal>\n      </AppContainer>\n    </>\n  )\n\n  // We catch runtime errors using componentDidCatch which will trigger renderError\n  renderReactElement(appElement!, (callback) => (\n    <Root callbacks={[callback, onRootCommit]}>\n      {process.env.__NEXT_STRICT_MODE ? (\n        <React.StrictMode>{elem}</React.StrictMode>\n      ) : (\n        elem\n      )}\n    </Root>\n  ))\n\n  return renderPromise\n}\n\nasync function render(renderingProps: RenderRouteInfo): Promise<void> {\n  // if an error occurs in a server-side page (e.g. in getInitialProps),\n  // skip re-rendering the error page client-side as data-fetching operations\n  // will already have been done on the server and NEXT_DATA contains the correct\n  // data for straight-forward hydration of the error page\n  if (\n    renderingProps.err &&\n    // renderingProps.Component might be undefined if there is a top/module-level error\n    (typeof renderingProps.Component === 'undefined' ||\n      !renderingProps.isHydratePass)\n  ) {\n    await renderError(renderingProps)\n    return\n  }\n\n  try {\n    await doRender(renderingProps)\n  } catch (err) {\n    const renderErr = getProperError(err)\n    // bubble up cancelation errors\n    if ((renderErr as Error & { cancelled?: boolean }).cancelled) {\n      throw renderErr\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      // Ensure this error is displayed in the overlay in development\n      setTimeout(() => {\n        throw renderErr\n      })\n    }\n    await renderError({ ...renderingProps, err: renderErr })\n  }\n}\n\nexport async function hydrate(opts?: { beforeRender?: () => Promise<void> }) {\n  let initialErr = initialData.err\n\n  try {\n    const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint('/_app')\n    if ('error' in appEntrypoint) {\n      throw appEntrypoint.error\n    }\n\n    const { component: app, exports: mod } = appEntrypoint\n    CachedApp = app as AppComponent\n    if (mod && mod.reportWebVitals) {\n      onPerfEntry = ({\n        id,\n        name,\n        startTime,\n        value,\n        duration,\n        entryType,\n        entries,\n        attribution,\n      }: any): void => {\n        // Combines timestamp with random number for unique ID\n        const uniqueID: string = `${Date.now()}-${\n          Math.floor(Math.random() * (9e12 - 1)) + 1e12\n        }`\n        let perfStartEntry: string | undefined\n\n        if (entries && entries.length) {\n          perfStartEntry = entries[0].startTime\n        }\n\n        const webVitals: NextWebVitalsMetric = {\n          id: id || uniqueID,\n          name,\n          startTime: startTime || perfStartEntry,\n          value: value == null ? duration : value,\n          label:\n            entryType === 'mark' || entryType === 'measure'\n              ? 'custom'\n              : 'web-vital',\n        }\n        if (attribution) {\n          webVitals.attribution = attribution\n        }\n        mod.reportWebVitals(webVitals)\n      }\n    }\n\n    const pageEntrypoint =\n      // The dev server fails to serve script assets when there's a hydration\n      // error, so we need to skip waiting for the entrypoint.\n      process.env.NODE_ENV === 'development' && initialData.err\n        ? { error: initialData.err }\n        : await pageLoader.routeLoader.whenEntrypoint(initialData.page)\n    if ('error' in pageEntrypoint) {\n      throw pageEntrypoint.error\n    }\n    CachedComponent = pageEntrypoint.component\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { isValidElementType } =\n        require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n      if (!isValidElementType(CachedComponent)) {\n        throw new Error(\n          `The default export is not a React Component in page: \"${initialData.page}\"`\n        )\n      }\n    }\n  } catch (error) {\n    // This catches errors like throwing in the top level of a module\n    initialErr = getProperError(error)\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    const getServerError = (\n      require('../server/dev/node-stack-frames') as typeof import('../server/dev/node-stack-frames')\n    ).getServerError\n    // Server-side runtime errors need to be re-thrown on the client-side so\n    // that the overlay is rendered.\n    if (initialErr) {\n      if (initialErr === initialData.err) {\n        setTimeout(() => {\n          let error\n          try {\n            // Generate a new error object. We `throw` it because some browsers\n            // will set the `stack` when thrown, and we want to ensure ours is\n            // not overridden when we re-throw it below.\n            throw new Error(initialErr!.message)\n          } catch (e) {\n            error = e as Error\n          }\n\n          error.name = initialErr!.name\n          error.stack = initialErr!.stack\n          const errSource = initialErr.source!\n\n          // In development, error the navigation API usage in runtime,\n          // since it's not allowed to be used in pages router as it doesn't contain error boundary like app router.\n          if (isNextRouterError(initialErr)) {\n            error.message =\n              'Next.js navigation API is not allowed to be used in Pages Router.'\n          }\n\n          throw getServerError(error, errSource)\n        })\n      }\n      // We replaced the server-side error with a client-side error, and should\n      // no longer rewrite the stack trace to a Node error.\n      else {\n        setTimeout(() => {\n          throw initialErr\n        })\n      }\n    }\n  }\n\n  if (window.__NEXT_PRELOADREADY) {\n    await window.__NEXT_PRELOADREADY(initialData.dynamicIds)\n  }\n\n  router = createRouter(initialData.page, initialData.query, asPath, {\n    initialProps: initialData.props,\n    pageLoader,\n    App: CachedApp,\n    Component: CachedComponent,\n    wrapApp,\n    err: initialErr,\n    isFallback: Boolean(initialData.isFallback),\n    subscription: (info, App, scroll) =>\n      render(\n        Object.assign<\n          {},\n          Omit<RenderRouteInfo, 'App' | 'scroll'>,\n          Pick<RenderRouteInfo, 'App' | 'scroll'>\n        >({}, info, {\n          App,\n          scroll,\n        }) as RenderRouteInfo\n      ),\n    locale: initialData.locale,\n    locales: initialData.locales,\n    defaultLocale,\n    domainLocales: initialData.domainLocales,\n    isPreview: initialData.isPreview,\n  })\n\n  initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise\n\n  const renderCtx: RenderRouteInfo = {\n    App: CachedApp,\n    initial: true,\n    Component: CachedComponent,\n    props: initialData.props,\n    err: initialErr,\n    isHydratePass: true,\n  }\n\n  if (opts?.beforeRender) {\n    await opts.beforeRender()\n  }\n\n  render(renderCtx)\n}\n"], "names": ["emitter", "hydrate", "initialize", "router", "version", "process", "env", "__NEXT_VERSION", "mitt", "looseToArray", "input", "slice", "call", "initialData", "defaultLocale", "undefined", "<PERSON><PERSON><PERSON>", "page<PERSON><PERSON>der", "appElement", "headManager", "initialMatchesMiddleware", "lastAppProps", "lastRenderReject", "devClient", "CachedApp", "onPerfEntry", "CachedComponent", "Container", "React", "Component", "componentDidCatch", "componentErr", "info", "props", "fn", "componentDidMount", "scrollToHash", "isSsr", "<PERSON><PERSON><PERSON><PERSON>", "nextExport", "isDynamicRoute", "pathname", "location", "search", "__NEXT_HAS_REWRITES", "__N_SSG", "replace", "String", "assign", "urlQueryToSearchParams", "query", "URLSearchParams", "_h", "shallow", "catch", "err", "cancelled", "componentDidUpdate", "hash", "substring", "el", "document", "getElementById", "setTimeout", "scrollIntoView", "render", "NODE_ENV", "children", "PagesDevOverlayBridge", "require", "opts", "tracer", "onSpanEnd", "default", "JSON", "parse", "textContent", "window", "__NEXT_DATA__", "prefix", "assetPrefix", "self", "__next_set_public_path__", "setConfig", "serverRuntimeConfig", "publicRuntimeConfig", "runtimeConfig", "getURL", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "__NEXT_I18N_SUPPORT", "normalizeLocalePath", "detectDomainLocale", "parseRelativeUrl", "formatUrl", "locales", "parsedAs", "localePathResult", "detectedLocale", "locale", "detectedDomain", "__NEXT_I18N_DOMAINS", "hostname", "<PERSON><PERSON><PERSON><PERSON>", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON>", "buildId", "register", "r", "f", "routeLoader", "onEntrypoint", "__NEXT_P", "map", "p", "push", "initHeadManager", "getIsSsr", "renderApp", "App", "appProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adaptedForAppRouter", "useMemo", "adaptForAppRouterInstance", "error", "renderError", "console", "AppRouterContext", "Provider", "value", "SearchParamsContext", "adaptForSearchParams", "PathnameContextProviderAdapter", "isAutoExport", "autoExport", "PathParamsContext", "adaptForPathParams", "RouterContext", "makePublicRouterInstance", "HeadManagerContext", "ImageConfigContext", "__NEXT_IMAGE_OPTS", "wrapApp", "wrappedAppProps", "renderErrorProps", "onUnrecoverableError", "doR<PERSON>", "styleSheets", "loadPage", "then", "page", "ErrorComponent", "errorModule", "appModule", "m", "AppTree", "appCtx", "ctx", "Promise", "resolve", "loadGetInitialProps", "initProps", "Head", "callback", "useLayoutEffect", "performanceMarks", "navigationStart", "beforeRender", "afterRender", "afterHydrate", "routeChange", "performanceMeasures", "hydration", "beforeHydration", "routeChangeToRender", "reactRoot", "shouldHydrate", "clearMarks", "for<PERSON>ach", "mark", "performance", "markHydrateComplete", "ST", "hasBeforeRenderMark", "getEntriesByName", "length", "beforeHydrationMeasure", "measure", "hydrationMeasure", "startSpan", "startTime", "<PERSON><PERSON><PERSON><PERSON>", "attributes", "end", "duration", "markRenderComplete", "navStartEntries", "name", "clearMeasures", "renderReactElement", "domEl", "reactEl", "ReactDOM", "hydrateRoot", "onRecoverableError", "startTransition", "Root", "callbacks", "__NEXT_TEST_MODE", "useEffect", "__NEXT_HYDRATED", "__NEXT_HYDRATED_AT", "now", "__NEXT_HYDRATED_CB", "canceled", "resolvePromise", "renderPromise", "reject", "Error", "onStart", "currentStyleTags", "querySelectorAll", "currentHrefs", "Set", "tag", "getAttribute", "noscript", "querySelector", "nonce", "href", "text", "has", "styleTag", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "createTextNode", "onHeadCommit", "TURBOPACK", "desiredHrefs", "s", "idx", "removeAttribute", "referenceNode", "targetTag", "parentNode", "insertBefore", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "scroll", "x", "y", "disableSmoothScrollDuringRouteTransition", "scrollTo", "onRootCommit", "elem", "Portal", "type", "RouteAnnouncer", "__NEXT_STRICT_MODE", "StrictMode", "renderingProps", "isHydratePass", "renderErr", "getProperError", "initialErr", "appEntrypoint", "whenEntrypoint", "component", "app", "exports", "mod", "reportWebVitals", "id", "entryType", "entries", "attribution", "uniqueID", "Date", "Math", "floor", "random", "perfStartEntry", "webVitals", "label", "pageEntrypoint", "isValidElementType", "getServerError", "message", "e", "stack", "errSource", "source", "isNextRouterError", "__NEXT_PRELOADREADY", "dynamicIds", "createRouter", "initialProps", "Boolean", "subscription", "Object", "domainLocales", "isPreview", "_initialMatchesMiddlewarePromise", "renderCtx", "initial"], "mappings": "AAAA,mBAAmB,GACnB,6DAA6D;;;;;;;;;;;;;;;;;;;IA0EhDA,OAAO;eAAPA;;IAiwBSC,OAAO;eAAPA;;IA9oBAC,UAAU;eAAVA;;IApHXC,MAAM;eAANA;;IADEC,OAAO;eAAPA;;;;;QAvEN;gEAQyB;iEACX;iDACc;+DAClB;4CAEa;qCAC2B;2BAC1B;6BAIxB;uCACmB;uBACsB;wBAEzB;sEACK;qEACL;gCAEQ;wBACwB;yBACxB;iDACI;gCAEJ;6BACH;+CACK;0BAM1B;iDAIA;oCAC4B;iEAChB;mCACe;AAwB3B,MAAMA,UAAUC,QAAQC,GAAG,CAACC,cAAc;AAC1C,IAAIJ;AACJ,MAAMH,UAA+BQ,IAAAA,aAAI;AAEhD,MAAMC,eAAe,CAAeC,QAAoB,EAAE,CAACC,KAAK,CAACC,IAAI,CAACF;AAEtE,IAAIG;AACJ,IAAIC,gBAAoCC;AACxC,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAKJ,IAAIC,2BAA2B;AAC/B,IAAIC;AAEJ,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,WAAyBC;AAC7B,IAAIC;AAEJ,MAAMC,kBAAkBC,cAAK,CAACC,SAAS;IAIrCC,kBAAkBC,YAAmB,EAAEC,IAAS,EAAE;QAChD,IAAI,CAACC,KAAK,CAACC,EAAE,CAACH,cAAcC;IAC9B;IAEAG,oBAAoB;QAClB,IAAI,CAACC,YAAY;QAEjB,0CAA0C;QAC1C,yEAAyE;QACzE,oEAAoE;QACpE,sDAAsD;QACtD,qEAAqE;QACrE,kEAAkE;QAClE,IACEjC,OAAOkC,KAAK,IACXxB,CAAAA,YAAYyB,UAAU,IACpBzB,YAAY0B,UAAU,IACpBC,CAAAA,IAAAA,yBAAc,EAACrC,OAAOsC,QAAQ,KAC7BC,SAASC,MAAM,IACftC,QAAQC,GAAG,CAACsC,mBAAmB,IAC/BxB,wBAAuB,KAC1BP,YAAYoB,KAAK,IAChBpB,YAAYoB,KAAK,CAACY,OAAO,IACxBH,CAAAA,SAASC,MAAM,IACdtC,QAAQC,GAAG,CAACsC,mBAAmB,IAC/BxB,wBAAuB,CAAE,GAC/B;YACA,2CAA2C;YAC3CjB,OACG2C,OAAO,CACN3C,OAAOsC,QAAQ,GACb,MACAM,OACEC,IAAAA,mBAAM,EACJC,IAAAA,mCAAsB,EAAC9C,OAAO+C,KAAK,GACnC,IAAIC,gBAAgBT,SAASC,MAAM,KAGzC3B,QACA;gBACE,aAAa;gBACb,0DAA0D;gBAC1D,oEAAoE;gBACpE,4CAA4C;gBAC5CoC,IAAI;gBACJ,mEAAmE;gBACnE,eAAe;gBACf,mEAAmE;gBACnE,yCAAyC;gBACzCC,SAAS,CAACxC,YAAYyB,UAAU,IAAI,CAAClB;YACvC,GAEDkC,KAAK,CAAC,CAACC;gBACN,IAAI,CAACA,IAAIC,SAAS,EAAE,MAAMD;YAC5B;QACJ;IACF;IAEAE,qBAAqB;QACnB,IAAI,CAACrB,YAAY;IACnB;IAEAA,eAAe;QACb,IAAI,EAAEsB,IAAI,EAAE,GAAGhB;QACfgB,OAAOA,QAAQA,KAAKC,SAAS,CAAC;QAC9B,IAAI,CAACD,MAAM;QAEX,MAAME,KAAyBC,SAASC,cAAc,CAACJ;QACvD,IAAI,CAACE,IAAI;QAET,2DAA2D;QAC3D,4BAA4B;QAC5BG,WAAW,IAAMH,GAAGI,cAAc,IAAI;IACxC;IAEAC,SAAS;QACP,IAAI5D,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,cAAc;YACzC,OAAO,IAAI,CAACjC,KAAK,CAACkC,QAAQ;QAC5B,OAAO;YACL,MAAM,EAAEC,qBAAqB,EAAE,GAC7BC,QAAQ;YACV,qBACE,qBAACD;0BAAuB,IAAI,CAACnC,KAAK,CAACkC,QAAQ;;QAE/C;IACF;AACF;AAEO,eAAejE,WAAWoE,IAA8B;IAA9BA,IAAAA,iBAAAA,OAA4B,CAAC;IAG5D,gEAAgE;IAChE,IAAIjE,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,eAAe;QAC1CK,eAAM,CAACC,SAAS,CACd,AACEH,QAAQ,8BACRI,OAAO;QAEXlD,YAAY+C,KAAK/C,SAAS;IAC5B;IAEAV,cAAc6D,KAAKC,KAAK,CACtBd,SAASC,cAAc,CAAC,iBAAkBc,WAAW;IAEvDC,OAAOC,aAAa,GAAGjE;IAEvBC,gBAAgBD,YAAYC,aAAa;IACzC,MAAMiE,SAAiBlE,YAAYmE,WAAW,IAAI;IAGhDC,KAAaC,wBAAwB,CAAC,AAAC,KAAEH,SAAO,WAAU,qBAAqB;;IAEjF,4DAA4D;IAC5DI,IAAAA,gCAAS,EAAC;QACRC,qBAAqB,CAAC;QACtBC,qBAAqBxE,YAAYyE,aAAa,IAAI,CAAC;IACrD;IAEAtE,SAASuE,IAAAA,aAAM;IAEf,uDAAuD;IACvD,IAAIC,IAAAA,wBAAW,EAACxE,SAAS;QACvBA,SAASyE,IAAAA,8BAAc,EAACzE;IAC1B;IAEA,IAAIX,QAAQC,GAAG,CAACoF,mBAAmB,EAAE;QACnC,MAAM,EAAEC,mBAAmB,EAAE,GAC3BtB,QAAQ;QAEV,MAAM,EAAEuB,kBAAkB,EAAE,GAC1BvB,QAAQ;QAEV,MAAM,EAAEwB,gBAAgB,EAAE,GACxBxB,QAAQ;QAEV,MAAM,EAAEyB,SAAS,EAAE,GACjBzB,QAAQ;QAEV,IAAIxD,YAAYkF,OAAO,EAAE;YACvB,MAAMC,WAAWH,iBAAiB7E;YAClC,MAAMiF,mBAAmBN,oBACvBK,SAASvD,QAAQ,EACjB5B,YAAYkF,OAAO;YAGrB,IAAIE,iBAAiBC,cAAc,EAAE;gBACnCF,SAASvD,QAAQ,GAAGwD,iBAAiBxD,QAAQ;gBAC7CzB,SAAS8E,UAAUE;YACrB,OAAO;gBACL,gEAAgE;gBAChE,kEAAkE;gBAClE,UAAU;gBACVlF,gBAAgBD,YAAYsF,MAAM;YACpC;YAEA,qDAAqD;YACrD,MAAMC,iBAAiBR,mBACrBvF,QAAQC,GAAG,CAAC+F,mBAAmB,EAC/BxB,OAAOnC,QAAQ,CAAC4D,QAAQ;YAG1B,iEAAiE;YACjE,0CAA0C;YAC1C,IAAIF,gBAAgB;gBAClBtF,gBAAgBsF,eAAetF,aAAa;YAC9C;QACF;IACF;IAEA,IAAID,YAAY0F,YAAY,EAAE;QAC5B,MAAM,EAAEC,gBAAgB,EAAE,GACxBnC,QAAQ;QACVmC,iBAAiB3F,YAAY0F,YAAY;IAC3C;IAEAtF,aAAa,IAAIwF,mBAAU,CAAC5F,YAAY6F,OAAO,EAAE3B;IAEjD,MAAM4B,WAAuB;YAAC,CAACC,GAAGC,EAAE;eAClC5F,WAAW6F,WAAW,CAACC,YAAY,CAACH,GAAGC;;IACzC,IAAIhC,OAAOmC,QAAQ,EAAE;QACnB,2EAA2E;QAC3E,qEAAqE;QACrEnC,OAAOmC,QAAQ,CAACC,GAAG,CAAC,CAACC,IAAMnD,WAAW,IAAM4C,SAASO,IAAI;IAC3D;IACArC,OAAOmC,QAAQ,GAAG,EAAE;IAClBnC,OAAOmC,QAAQ,CAASG,IAAI,GAAGR;IAEjCxF,cAAciG,IAAAA,oBAAe;IAC7BjG,YAAYkG,QAAQ,GAAG;QACrB,OAAOlH,OAAOkC,KAAK;IACrB;IAEAnB,aAAa2C,SAASC,cAAc,CAAC;IACrC,OAAO;QAAEkB,aAAaD;IAAO;AAC/B;AAEA,SAASuC,UAAUC,GAAiB,EAAEC,QAAkB;IACtD,qBAAO,qBAACD;QAAK,GAAGC,QAAQ;;AAC1B;AAEA,SAASC,aAAa,KAEQ;IAFR,IAAA,EACpBtD,QAAQ,EACoB,GAFR;IAGpB,8DAA8D;IAC9D,MAAMuD,sBAAsB9F,cAAK,CAAC+F,OAAO,CAAC;QACxC,OAAOC,IAAAA,mCAAyB,EAACzH;IACnC,GAAG,EAAE;QAemB8E;IAdxB,qBACE,qBAACtD;QACCO,IAAI,CAAC2F,QACH,iCAAiC;YACjC,mEAAmE;YACnEC,YAAY;gBAAEP,KAAK/F;gBAAW+B,KAAKsE;YAAM,GAAGvE,KAAK,CAAC,CAACC,MACjDwE,QAAQF,KAAK,CAAC,0BAA0BtE;kBAI5C,cAAA,qBAACyE,+CAAgB,CAACC,QAAQ;YAACC,OAAOR;sBAChC,cAAA,qBAACS,oDAAmB,CAACF,QAAQ;gBAACC,OAAOE,IAAAA,8BAAoB,EAACjI;0BACxD,cAAA,qBAACkI,wCAA8B;oBAC7BlI,QAAQA;oBACRmI,cAAcrD,CAAAA,iCAAAA,KAAKH,aAAa,CAACyD,UAAU,YAA7BtD,iCAAiC;8BAE/C,cAAA,qBAACuD,kDAAiB,CAACP,QAAQ;wBAACC,OAAOO,IAAAA,4BAAkB,EAACtI;kCACpD,cAAA,qBAACuI,yCAAa,CAACT,QAAQ;4BAACC,OAAOS,IAAAA,gCAAwB,EAACxI;sCACtD,cAAA,qBAACyI,mDAAkB,CAACX,QAAQ;gCAACC,OAAO/G;0CAClC,cAAA,qBAAC0H,mDAAkB,CAACZ,QAAQ;oCAC1BC,OACE7H,QAAQC,GAAG,CACRwI,iBAAiB;8CAGrB3E;;;;;;;;;AAUrB;AAEA,MAAM4E,UACJ,CAACxB,MACD,CAACyB;QACC,MAAMxB,WAAqB;YACzB,GAAGwB,eAAe;YAClBnH,WAAWH;YACX6B,KAAK1C,YAAY0C,GAAG;YACpBpD;QACF;QACA,qBAAO,qBAACsH;sBAAcH,UAAUC,KAAKC;;IACvC;AAEF,oDAAoD;AACpD,gDAAgD;AAChD,wDAAwD;AACxD,SAASM,YAAYmB,gBAAkC;IACrD,IAAI,EAAE1B,GAAG,EAAEhE,GAAG,EAAE,GAAG0F;IAEnB,0DAA0D;IAC1D,+FAA+F;IAC/F,IAAI5I,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,cAAc;QACzC,4DAA4D;QAC5D,sEAAsE;QACtE3C,UAAU2H,oBAAoB;QAE9B,uEAAuE;QACvE,iBAAiB;QACjB,iCAAiC;QACjC,mEAAmE;QACnE,OAAOC,SAAS;YACd5B,KAAK,IAAM;YACXtF,OAAO,CAAC;YACRJ,WAAW,IAAM;YACjBuH,aAAa,EAAE;QACjB;IACF;IAEA,sFAAsF;IACtFrB,QAAQF,KAAK,CAACtE;IACdwE,QAAQF,KAAK,CACV;IAGH,OAAO5G,WACJoI,QAAQ,CAAC,WACTC,IAAI,CAAC;YAAC,EAAEC,MAAMC,cAAc,EAAEJ,WAAW,EAAE;QAC1C,OAAO/H,CAAAA,gCAAAA,aAAcQ,SAAS,MAAK2H,iBAC/B,MAAM,CAAC,mBACJF,IAAI,CAAC,CAACG;YACL,OAAO,MAAM,CAAC,iBAAiBH,IAAI,CAAC,CAACI;gBACnCnC,MAAMmC,UAAUjF,OAAO;gBACvBwE,iBAAiB1B,GAAG,GAAGA;gBACvB,OAAOkC;YACT;QACF,GACCH,IAAI,CAAC,CAACK,IAAO,CAAA;gBACZH,gBAAgBG,EAAElF,OAAO;gBACzB2E,aAAa,EAAE;YACjB,CAAA,KACF;YAAEI;YAAgBJ;QAAY;IACpC,GACCE,IAAI,CAAC;YAAC,EAAEE,cAAc,EAAEJ,WAAW,EAAE;YAkBlCH;QAjBF,8EAA8E;QAC9E,kFAAkF;QAClF,yEAAyE;QACzE,MAAMW,UAAUb,QAAQxB;QACxB,MAAMsC,SAAS;YACbhI,WAAW2H;YACXI;YACAzJ;YACA2J,KAAK;gBACHvG;gBACAd,UAAU5B,YAAY0I,IAAI;gBAC1BrG,OAAOrC,YAAYqC,KAAK;gBACxBlC;gBACA4I;YACF;QACF;QACA,OAAOG,QAAQC,OAAO,CACpBf,EAAAA,0BAAAA,iBAAiBhH,KAAK,qBAAtBgH,wBAAwB1F,GAAG,IACvB0F,iBAAiBhH,KAAK,GACtBgI,IAAAA,0BAAmB,EAAC1C,KAAKsC,SAC7BP,IAAI,CAAC,CAACY,YACN,iCAAiC;YACjC,mEAAmE;YACnEf,SAAS;gBACP,GAAGF,gBAAgB;gBACnB1F;gBACA1B,WAAW2H;gBACXJ;gBACAnH,OAAOiI;YACT;IAEJ;AACJ;AAEA,mEAAmE;AACnE,yDAAyD;AACzD,SAASC,KAAK,KAAsC;IAAtC,IAAA,EAAEC,QAAQ,EAA4B,GAAtC;IACZ,iEAAiE;IACjE,uCAAuC;IACvCxI,cAAK,CAACyI,eAAe,CAAC,IAAMD,YAAY;QAACA;KAAS;IAClD,OAAO;AACT;AAEA,MAAME,mBAAmB;IACvBC,iBAAiB;IACjBC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,aAAa;AACf;AAEA,MAAMC,sBAAsB;IAC1BC,WAAW;IACXC,iBAAiB;IACjBC,qBAAqB;IACrB9G,QAAQ;AACV;AAEA,IAAI+G,YAAiB;AACrB,mDAAmD;AACnD,IAAIC,gBAAyB;AAE7B,SAASC;;IACN;QACCZ,iBAAiBE,YAAY;QAC7BF,iBAAiBI,YAAY;QAC7BJ,iBAAiBG,WAAW;QAC5BH,iBAAiBK,WAAW;KAC7B,CAACQ,OAAO,CAAC,CAACC,OAASC,YAAYH,UAAU,CAACE;AAC7C;AAEA,SAASE;IACP,IAAI,CAACC,SAAE,EAAE;IAETF,YAAYD,IAAI,CAACd,iBAAiBI,YAAY,EAAE,wBAAwB;;IAExE,MAAMc,sBAAsBH,YAAYI,gBAAgB,CACtDnB,iBAAiBE,YAAY,EAC7B,QACAkB,MAAM;IACR,IAAIF,qBAAqB;QACvB,MAAMG,yBAAyBN,YAAYO,OAAO,CAChDhB,oBAAoBE,eAAe,EACnCR,iBAAiBC,eAAe,EAChCD,iBAAiBE,YAAY;QAG/B,MAAMqB,mBAAmBR,YAAYO,OAAO,CAC1ChB,oBAAoBC,SAAS,EAC7BP,iBAAiBE,YAAY,EAC7BF,iBAAiBI,YAAY;QAG/B,IACErK,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,iBACzB,yFAAyF;QACzFyH,0BACAE,kBACA;YACAtH,eAAM,CACHuH,SAAS,CAAC,2BAA2B;gBACpCC,WAAWV,YAAYW,UAAU,GAAGL,uBAAuBI,SAAS;gBACpEE,YAAY;oBACVxJ,UAAUC,SAASD,QAAQ;oBAC3BS,OAAOR,SAASC,MAAM;gBACxB;YACF,GACCuJ,GAAG,CACFb,YAAYW,UAAU,GACpBH,iBAAiBE,SAAS,GAC1BF,iBAAiBM,QAAQ;QAEjC;IACF;IAEA,IAAI1K,aAAa;QACf4J,YACGI,gBAAgB,CAACb,oBAAoBC,SAAS,EAC9CM,OAAO,CAAC1J;IACb;IACAyJ;AACF;AAEA,SAASkB;IACP,IAAI,CAACb,SAAE,EAAE;IAETF,YAAYD,IAAI,CAACd,iBAAiBG,WAAW,EAAE,qBAAqB;;IACpE,MAAM4B,kBAAwChB,YAAYI,gBAAgB,CACxEnB,iBAAiBK,WAAW,EAC5B;IAGF,IAAI,CAAC0B,gBAAgBX,MAAM,EAAE;IAE7B,MAAMF,sBAAsBH,YAAYI,gBAAgB,CACtDnB,iBAAiBE,YAAY,EAC7B,QACAkB,MAAM;IAER,IAAIF,qBAAqB;QACvBH,YAAYO,OAAO,CACjBhB,oBAAoBG,mBAAmB,EACvCsB,eAAe,CAAC,EAAE,CAACC,IAAI,EACvBhC,iBAAiBE,YAAY;QAE/Ba,YAAYO,OAAO,CACjBhB,oBAAoB3G,MAAM,EAC1BqG,iBAAiBE,YAAY,EAC7BF,iBAAiBG,WAAW;QAE9B,IAAIhJ,aAAa;YACf4J,YACGI,gBAAgB,CAACb,oBAAoB3G,MAAM,EAC3CkH,OAAO,CAAC1J;YACX4J,YACGI,gBAAgB,CAACb,oBAAoBG,mBAAmB,EACxDI,OAAO,CAAC1J;QACb;IACF;IAEAyJ;IACC;QACCN,oBAAoBG,mBAAmB;QACvCH,oBAAoB3G,MAAM;KAC3B,CAACkH,OAAO,CAAC,CAACS,UAAYP,YAAYkB,aAAa,CAACX;AACnD;AAEA,SAASY,mBACPC,KAAkB,EAClBvK,EAAmC;IAEnC,+BAA+B;IAC/B,IAAIqJ,SAAE,EAAE;QACNF,YAAYD,IAAI,CAACd,iBAAiBE,YAAY;IAChD;IAEA,MAAMkC,UAAUxK,GAAG+I,gBAAgBK,sBAAsBc;IACzD,IAAI,CAACpB,WAAW;QACd,4EAA4E;QAC5EA,YAAY2B,eAAQ,CAACC,WAAW,CAACH,OAAOC,SAAS;YAC/CG,oBAAAA,sCAAkB;QACpB;QACA,uGAAuG;QACvG5B,gBAAgB;IAClB,OAAO;QACL,MAAM6B,kBAAkB,AAAClL,cAAK,CAASkL,eAAe;QACtDA,gBAAgB;YACd9B,UAAU/G,MAAM,CAACyI;QACnB;IACF;AACF;AAEA,SAASK,KAAK,KAKZ;IALY,IAAA,EACZC,SAAS,EACT7I,QAAQ,EAGR,GALY;IAMZ,mEAAmE;IACnE,sCAAsC;IACtCvC,cAAK,CAACyI,eAAe,CACnB,IAAM2C,UAAU7B,OAAO,CAAC,CAACf,WAAaA,aACtC;QAAC4C;KAAU;IAGb,IAAI3M,QAAQC,GAAG,CAAC2M,gBAAgB,EAAE;QAChC,sDAAsD;QACtDrL,cAAK,CAACsL,SAAS,CAAC;YACdrI,OAAOsI,eAAe,GAAG;YACzBtI,OAAOuI,kBAAkB,GAAG/B,YAAYgC,GAAG;YAE3C,IAAIxI,OAAOyI,kBAAkB,EAAE;gBAC7BzI,OAAOyI,kBAAkB;YAC3B;QACF,GAAG,EAAE;IACP;IAEA,OAAOnJ;AACT;AAEA,SAASgF,SAASzI,KAAsB;IACtC,IAAI,EAAE6G,GAAG,EAAE1F,SAAS,EAAEI,KAAK,EAAEsB,GAAG,EAAE,GAAoB7C;IACtD,IAAI0I,cACF,aAAa1I,QAAQK,YAAYL,MAAM0I,WAAW;IACpDvH,YAAYA,aAAaR,aAAaQ,SAAS;IAC/CI,QAAQA,SAASZ,aAAaY,KAAK;IAEnC,MAAMuF,WAAqB;QACzB,GAAGvF,KAAK;QACRJ;QACA0B;QACApD;IACF;IACA,+FAA+F;IAC/FkB,eAAemG;IAEf,IAAI+F,WAAoB;IACxB,IAAIC;IACJ,MAAMC,gBAAgB,IAAI1D,QAAc,CAACC,SAAS0D;QAChD,IAAIpM,kBAAkB;YACpBA;QACF;QACAkM,iBAAiB;YACflM,mBAAmB;YACnB0I;QACF;QACA1I,mBAAmB;YACjBiM,WAAW;YACXjM,mBAAmB;YAEnB,MAAMuG,QAAa,qBAAmC,CAAnC,IAAI8F,MAAM,2BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAkC;YACrD9F,MAAMrE,SAAS,GAAG;YAClBkK,OAAO7F;QACT;IACF;IAEA,yEAAyE;IACzE,yCAAyC;IACzC,SAAS+F;QACP,IACE,CAACxE,eACD,wEAAwE;QACxE,8BAA8B;QAC9B/I,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,cACzB;YACA,OAAO;QACT;QAEA,MAAM2J,mBAAuCpN,aAC3CoD,SAASiK,gBAAgB,CAAC;QAE5B,MAAMC,eAAmC,IAAIC,IAC3CH,iBAAiB5G,GAAG,CAAC,CAACgH,MAAQA,IAAIC,YAAY,CAAC;QAGjD,MAAMC,WAA2BtK,SAASuK,aAAa,CACrD;QAEF,MAAMC,QACJF,4BAAAA,SAAUD,YAAY,CAAC;QAEzB9E,YAAY+B,OAAO,CAAC;gBAAC,EAAEmD,IAAI,EAAEC,IAAI,EAA+B;YAC9D,IAAI,CAACR,aAAaS,GAAG,CAACF,OAAO;gBAC3B,MAAMG,WAAW5K,SAAS6K,aAAa,CAAC;gBACxCD,SAASE,YAAY,CAAC,eAAeL;gBACrCG,SAASE,YAAY,CAAC,SAAS;gBAE/B,IAAIN,OAAO;oBACTI,SAASE,YAAY,CAAC,SAASN;gBACjC;gBAEAxK,SAAS+K,IAAI,CAACC,WAAW,CAACJ;gBAC1BA,SAASI,WAAW,CAAChL,SAASiL,cAAc,CAACP;YAC/C;QACF;QACA,OAAO;IACT;IAEA,SAASQ;QACP,IACE,qFAAqF;QACrF,CAAC1O,QAAQC,GAAG,CAAC0O,SAAS,IACtB,wEAAwE;QACxE,8BAA8B;QAC9B3O,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,gBACzB,yEAAyE;QACzE,sCAAsC;QACtCkF,eACA,sCAAsC;QACtC,CAACmE,UACD;YACA,MAAM0B,eAA4B,IAAIjB,IAAI5E,YAAYnC,GAAG,CAAC,CAACiI,IAAMA,EAAEZ,IAAI;YACvE,MAAMT,mBACJpN,aACEoD,SAASiK,gBAAgB,CAAC;YAE9B,MAAMC,eAAyBF,iBAAiB5G,GAAG,CACjD,CAACgH,MAAQA,IAAIC,YAAY,CAAC;YAG5B,kEAAkE;YAClE,IAAK,IAAIiB,MAAM,GAAGA,MAAMpB,aAAarC,MAAM,EAAE,EAAEyD,IAAK;gBAClD,IAAIF,aAAaT,GAAG,CAACT,YAAY,CAACoB,IAAI,GAAG;oBACvCtB,gBAAgB,CAACsB,IAAI,CAACC,eAAe,CAAC;gBACxC,OAAO;oBACLvB,gBAAgB,CAACsB,IAAI,CAACR,YAAY,CAAC,SAAS;gBAC9C;YACF;YAEA,sCAAsC;YACtC,IAAIU,gBAAgCxL,SAASuK,aAAa,CACxD;YAEF,IACE,+BAA+B;YAC/BiB,eACA;gBACAjG,YAAY+B,OAAO,CAAC;wBAAC,EAAEmD,IAAI,EAAoB;oBAC7C,MAAMgB,YAA4BzL,SAASuK,aAAa,CACtD,AAAC,wBAAqBE,OAAK;oBAE7B,IACE,+BAA+B;oBAC/BgB,WACA;wBACAD,cAAeE,UAAU,CAAEC,YAAY,CACrCF,WACAD,cAAeI,WAAW;wBAE5BJ,gBAAgBC;oBAClB;gBACF;YACF;YAEA,iDAAiD;YACjD7O,aACEoD,SAASiK,gBAAgB,CAAC,mBAC1B3C,OAAO,CAAC,CAACvH;gBACTA,GAAG2L,UAAU,CAAEG,WAAW,CAAC9L;YAC7B;QACF;QAEA,IAAIlD,MAAMiP,MAAM,EAAE;YAChB,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGnP,MAAMiP,MAAM;YAC7BG,IAAAA,6DAAwC,EAAC;gBACvCjL,OAAOkL,QAAQ,CAACH,GAAGC;YACrB;QACF;IACF;IAEA,SAASG;QACPxC;IACF;IAEAI;IAEA,MAAMqC,qBACJ;;0BACE,qBAAC9F;gBAAKC,UAAU2E;;0BAChB,sBAACtH;;oBACEH,UAAUC,KAAKC;kCAChB,qBAAC0I,cAAM;wBAACC,MAAK;kCACX,cAAA,qBAACC,8BAAc;;;;;;IAMvB,iFAAiF;IACjF5D,mBAAmBtL,YAAa,CAACkJ,yBAC/B,qBAAC2C;YAAKC,WAAW;gBAAC5C;gBAAU4F;aAAa;sBACtC3P,QAAQC,GAAG,CAAC+P,kBAAkB,iBAC7B,qBAACzO,cAAK,CAAC0O,UAAU;0BAAEL;iBAEnBA;;IAKN,OAAOxC;AACT;AAEA,eAAexJ,OAAOsM,cAA+B;IACnD,sEAAsE;IACtE,2EAA2E;IAC3E,+EAA+E;IAC/E,wDAAwD;IACxD,IACEA,eAAehN,GAAG,IAClB,mFAAmF;IAClF,CAAA,OAAOgN,eAAe1O,SAAS,KAAK,eACnC,CAAC0O,eAAeC,aAAa,AAAD,GAC9B;QACA,MAAM1I,YAAYyI;QAClB;IACF;IAEA,IAAI;QACF,MAAMpH,SAASoH;IACjB,EAAE,OAAOhN,KAAK;QACZ,MAAMkN,YAAYC,IAAAA,uBAAc,EAACnN;QACjC,+BAA+B;QAC/B,IAAI,AAACkN,UAA8CjN,SAAS,EAAE;YAC5D,MAAMiN;QACR;QAEA,IAAIpQ,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,eAAe;YAC1C,+DAA+D;YAC/DH,WAAW;gBACT,MAAM0M;YACR;QACF;QACA,MAAM3I,YAAY;YAAE,GAAGyI,cAAc;YAAEhN,KAAKkN;QAAU;IACxD;AACF;AAEO,eAAexQ,QAAQqE,IAA6C;IACzE,IAAIqM,aAAa9P,YAAY0C,GAAG;IAEhC,IAAI;QACF,MAAMqN,gBAAgB,MAAM3P,WAAW6F,WAAW,CAAC+J,cAAc,CAAC;QAClE,IAAI,WAAWD,eAAe;YAC5B,MAAMA,cAAc/I,KAAK;QAC3B;QAEA,MAAM,EAAEiJ,WAAWC,GAAG,EAAEC,SAASC,GAAG,EAAE,GAAGL;QACzCpP,YAAYuP;QACZ,IAAIE,OAAOA,IAAIC,eAAe,EAAE;YAC9BzP,cAAc;oBAAC,EACb0P,EAAE,EACF7E,IAAI,EACJP,SAAS,EACT7D,KAAK,EACLiE,QAAQ,EACRiF,SAAS,EACTC,OAAO,EACPC,WAAW,EACP;gBACJ,sDAAsD;gBACtD,MAAMC,WAAmB,AAAGC,KAAKnE,GAAG,KAAG,MACrCoE,CAAAA,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAM,CAAA,OAAO,CAAA,KAAM,IAAG;gBAE9C,IAAIC;gBAEJ,IAAIP,WAAWA,QAAQ3F,MAAM,EAAE;oBAC7BkG,iBAAiBP,OAAO,CAAC,EAAE,CAACtF,SAAS;gBACvC;gBAEA,MAAM8F,YAAiC;oBACrCV,IAAIA,MAAMI;oBACVjF;oBACAP,WAAWA,aAAa6F;oBACxB1J,OAAOA,SAAS,OAAOiE,WAAWjE;oBAClC4J,OACEV,cAAc,UAAUA,cAAc,YAClC,WACA;gBACR;gBACA,IAAIE,aAAa;oBACfO,UAAUP,WAAW,GAAGA;gBAC1B;gBACAL,IAAIC,eAAe,CAACW;YACtB;QACF;QAEA,MAAME,iBACJ,uEAAuE;QACvE,wDAAwD;QACxD1R,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,iBAAiBrD,YAAY0C,GAAG,GACrD;YAAEsE,OAAOhH,YAAY0C,GAAG;QAAC,IACzB,MAAMtC,WAAW6F,WAAW,CAAC+J,cAAc,CAAChQ,YAAY0I,IAAI;QAClE,IAAI,WAAWwI,gBAAgB;YAC7B,MAAMA,eAAelK,KAAK;QAC5B;QACAnG,kBAAkBqQ,eAAejB,SAAS;QAE1C,IAAIzQ,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAE8N,kBAAkB,EAAE,GAC1B3N,QAAQ;YACV,IAAI,CAAC2N,mBAAmBtQ,kBAAkB;gBACxC,MAAM,qBAEL,CAFK,IAAIiM,MACR,AAAC,2DAAwD9M,YAAY0I,IAAI,GAAC,MADtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF,EAAE,OAAO1B,OAAO;QACd,iEAAiE;QACjE8I,aAAaD,IAAAA,uBAAc,EAAC7I;IAC9B;IAEA,IAAIxH,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,eAAe;QAC1C,MAAM+N,iBAAiB,AACrB5N,QAAQ,mCACR4N,cAAc;QAChB,wEAAwE;QACxE,gCAAgC;QAChC,IAAItB,YAAY;YACd,IAAIA,eAAe9P,YAAY0C,GAAG,EAAE;gBAClCQ,WAAW;oBACT,IAAI8D;oBACJ,IAAI;wBACF,mEAAmE;wBACnE,kEAAkE;wBAClE,4CAA4C;wBAC5C,MAAM,qBAA8B,CAA9B,IAAI8F,MAAMgD,WAAYuB,OAAO,GAA7B,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6B;oBACrC,EAAE,OAAOC,GAAG;wBACVtK,QAAQsK;oBACV;oBAEAtK,MAAMyE,IAAI,GAAGqE,WAAYrE,IAAI;oBAC7BzE,MAAMuK,KAAK,GAAGzB,WAAYyB,KAAK;oBAC/B,MAAMC,YAAY1B,WAAW2B,MAAM;oBAEnC,6DAA6D;oBAC7D,0GAA0G;oBAC1G,IAAIC,IAAAA,oCAAiB,EAAC5B,aAAa;wBACjC9I,MAAMqK,OAAO,GACX;oBACJ;oBAEA,MAAMD,eAAepK,OAAOwK;gBAC9B;YACF,OAGK;gBACHtO,WAAW;oBACT,MAAM4M;gBACR;YACF;QACF;IACF;IAEA,IAAI9L,OAAO2N,mBAAmB,EAAE;QAC9B,MAAM3N,OAAO2N,mBAAmB,CAAC3R,YAAY4R,UAAU;IACzD;IAEAtS,SAASuS,IAAAA,oBAAY,EAAC7R,YAAY0I,IAAI,EAAE1I,YAAYqC,KAAK,EAAElC,QAAQ;QACjE2R,cAAc9R,YAAYoB,KAAK;QAC/BhB;QACAsG,KAAK/F;QACLK,WAAWH;QACXqH;QACAxF,KAAKoN;QACLrO,YAAYsQ,QAAQ/R,YAAYyB,UAAU;QAC1CuQ,cAAc,CAAC7Q,MAAMuF,KAAKoI,SACxB1L,OACE6O,OAAO9P,MAAM,CAIX,CAAC,GAAGhB,MAAM;gBACVuF;gBACAoI;YACF;QAEJxJ,QAAQtF,YAAYsF,MAAM;QAC1BJ,SAASlF,YAAYkF,OAAO;QAC5BjF;QACAiS,eAAelS,YAAYkS,aAAa;QACxCC,WAAWnS,YAAYmS,SAAS;IAClC;IAEA5R,2BAA2B,MAAMjB,OAAO8S,gCAAgC;IAExE,MAAMC,YAA6B;QACjC3L,KAAK/F;QACL2R,SAAS;QACTtR,WAAWH;QACXO,OAAOpB,YAAYoB,KAAK;QACxBsB,KAAKoN;QACLH,eAAe;IACjB;IAEA,IAAIlM,wBAAAA,KAAMkG,YAAY,EAAE;QACtB,MAAMlG,KAAKkG,YAAY;IACzB;IAEAvG,OAAOiP;AACT", "ignoreList": [0]}