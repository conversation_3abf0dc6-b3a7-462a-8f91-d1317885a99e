import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ServiceManager } from '@/lib/services/manager';
import { z } from 'zod';

const serviceManager = new ServiceManager();

// GET /api/services - Get available services
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters = {
      category: searchParams.get('category') || undefined,
      brand: searchParams.get('brand') || undefined,
      search_term: searchParams.get('search') || undefined,
      price_min: searchParams.get('price_min') ? parseFloat(searchParams.get('price_min')!) : undefined,
      price_max: searchParams.get('price_max') ? parseFloat(searchParams.get('price_max')!) : undefined,
    };

    const services = await serviceManager.getAvailableServices(filters);

    return NextResponse.json({
      success: true,
      services,
      count: services.length
    });

  } catch (error) {
    console.error('Error fetching services:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/services/validate - Validate service request
const validateSchema = z.object({
  service_id: z.string().uuid('Invalid service ID'),
  device_info: z.object({
    brand: z.string().min(1, 'Brand is required'),
    model: z.string().min(1, 'Model is required'),
    imei: z.string().optional(),
    serial_number: z.string().optional(),
    android_version: z.string().optional(),
    ios_version: z.string().optional(),
    additional_info: z.record(z.any()).optional()
  })
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { service_id, device_info } = validateSchema.parse(body);

    const validation = await serviceManager.validateServiceRequest(service_id, device_info);

    return NextResponse.json({
      success: true,
      validation
    });

  } catch (error: any) {
    console.error('Error validating service:', error);

    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
