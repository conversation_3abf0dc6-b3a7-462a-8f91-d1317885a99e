import type { Metadata } from "next";
import "./globals.css";
import Header from "@/components/layout/Header";
import Sidebar from "@/components/layout/Sidebar";

export const metadata: Metadata = {
  title: "FRP Reseller Platform - Servicios FRP y Reparación IMEI",
  description: "Plataforma profesional para reventa de servicios FRP bypass y reparación IMEI en Argentina",
  keywords: "FRP, IMEI, bypass, unlock, reparación, celulares, Argentina",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // TODO: Get user from session/auth
  const user = null; // This will be replaced with actual auth

  return (
    <html lang="es">
      <body className="antialiased">
        <div className="min-h-screen bg-gray-50">
          <Header user={user} />

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex gap-6">
              {/* Main content */}
              <main className="flex-1">
                {children}
              </main>

              {/* Sidebar */}
              <Sidebar user={user} />
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
